@{
  # Webserver root share\directory
  ServerRoot = '\\softtsrv\Sites'
  Labs       = @(
    <# Interurban, Centre for Business & Access CBA119A #>
    @{
      GUID           = '47eb4ea7-ae3c-4f58-9ad5-1dbfdc4f3bc1'
      Department     = 'Interurban, Centre for Business & Access'
      LabNames       = @('CBA119A')
      IsGroup        = $false
      SamAccountName = 'CBA119A_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Centre for Business & Access CBA124A #>
    @{
      GUID           = 'f470dc0c-be3a-4db8-9b73-72ab61010968'
      Department     = 'Interurban, Centre for Business & Access'
      LabNames       = @('CBA124A')
      IsGroup        = $false
      SamAccountName = 'CBA124A_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Centre for Business & Access CBA144 #>
    @{
      GUID           = '1ed99047-89bc-441a-b8ab-421e82112e3c'
      Department     = 'Interurban, Centre for Business & Access'
      LabNames       = @('CBA144')
      IsGroup        = $false
      SamAccountName = 'CBA144_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Centre for Business & Access CBA159 #>
    @{
      GUID           = '4ee18a51-3c45-4bb3-ab72-1742ca8d7188'
      Department     = 'Interurban, Centre for Business & Access'
      LabNames       = @('CBA159')
      IsGroup        = $false
      SamAccountName = 'CBA159_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Centre for Business & Access CBA160 #>
    @{
      GUID           = '2b5a4d1a-e74e-4afa-a6f0-fb85f8e6ac40'
      Department     = 'Interurban, Centre for Business & Access'
      LabNames       = @('CBA160')
      IsGroup        = $false
      SamAccountName = 'CBA160_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Centre for Business & Access CBA201 #>
    @{
      GUID           = 'd7abeda7-9659-4f3c-b163-3e69db74dda2'
      Department     = 'Interurban, Centre for Business & Access'
      LabNames       = @('CBA201')
      IsGroup        = $false
      SamAccountName = 'CBA201_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Centre for Business & Access CBA202 #>
    @{
      GUID           = 'f0a0d23e-af66-422c-a3f3-7bb196d016be'
      Department     = 'Interurban, Centre for Business & Access'
      LabNames       = @('CBA202')
      IsGroup        = $false
      SamAccountName = 'CBA202_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Centre for Business & Access CBA214 #>
    @{
      GUID           = 'cd4ca89e-19b6-42d7-9d50-0a3cb27c618f'
      Department     = 'Interurban, Centre for Business & Access'
      LabNames       = @('CBA214')
      IsGroup        = $false
      SamAccountName = 'CBA214_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Centre for Business & Access CBA219 #>
    @{
      GUID           = 'ad4af0e2-2630-4754-9fcc-05849fbc9f5c'
      Department     = 'Interurban, Centre for Business & Access'
      LabNames       = @('CBA219')
      IsGroup        = $false
      SamAccountName = 'CBA219_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Centre for Business & Access CBA220 #>
    @{
      GUID           = '4b0ac553-1c91-4f72-82f4-3932c22fae4d'
      Department     = 'Interurban, Centre for Business & Access'
      LabNames       = @('CBA220')
      IsGroup        = $false
      SamAccountName = 'CBA220_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Centre for Business & Access CBA277 #>
    @{
      GUID           = 'a908c72c-301e-4db4-8179-878f42ef21f6'
      Department     = 'Interurban, Centre for Business & Access'
      LabNames       = @('CBA277')
      IsGroup        = $false
      SamAccountName = 'CBA277_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Centre for Business & Access CBA282 #>
    @{
      GUID           = '77661db6-e99d-4f76-b416-8654c29efbac'
      Department     = 'Interurban, Centre for Business & Access'
      LabNames       = @('CBA282')
      IsGroup        = $false
      SamAccountName = 'CBA282_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Centre for Business & Access CBA287 #>
    @{
      GUID           = '982c2603-edba-46d7-8e2e-2e63600bbb87'
      Department     = 'Interurban, Centre for Business & Access'
      LabNames       = @('CBA287')
      IsGroup        = $false
      SamAccountName = 'CBA287_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Centre for Health and Wellness  CHW133 #>
    @{
      GUID           = '45121c79-bfe1-4e39-95ec-ed7b0468bb67'
      Department     = 'Interurban, Centre for Health and Wellness '
      LabNames       = @('CHW133')
      IsGroup        = $false
      SamAccountName = 'CHW133_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Centre for Trades Education and Innovation  CTEI119-Automotive # >
    @{
      GUID           = '********-1d53-46d5-91af-2f13888b2e11'
      Department     = 'Interurban, Centre for Trades Education and Innovation '
      LabNames       = @('CTEI119-Automotive')
      IsGroup        = $false
      SamAccountName = 'CTEI119-Automotive_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Centre for Trades Education and Innovation  CTEI124a #>
    @{
      GUID           = '307628ff-b8bd-47bb-b638-5eb4c1a5ff08'
      Department     = 'Interurban, Centre for Trades Education and Innovation '
      LabNames       = @('CTEI124a')
      IsGroup        = $false
      SamAccountName = 'CTEI124a_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Centre for Trades Education and Innovation  CTEI124b #>
    @{
      GUID           = 'c4d2e1a6-d35f-4983-a33d-21d1a808cf09'
      Department     = 'Interurban, Centre for Trades Education and Innovation '
      LabNames       = @('CTEI124b')
      IsGroup        = $false
      SamAccountName = 'CTEI124b_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, LACC ILC340 #>
    @{
      GUID           = '39bbc4ea-c549-46c1-85c5-09aadcd51dbc'
      Department     = 'Interurban, LACC'
      LabNames       = @('ILC340')
      IsGroup        = $false
      SamAccountName = 'ILC340_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, LACC ILC345 #>
    @{
      GUID           = 'a2292d7c-f405-4907-8c30-cb8705d6b687'
      Department     = 'Interurban, LACC'
      LabNames       = @('ILC345')
      IsGroup        = $false
      SamAccountName = 'ILC345_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Jack White Building JWT100D4 #>
    @{
      GUID           = '8e34398c-dea9-4dbd-854b-999396aff9de'
      Department     = 'Interurban, Jack White Building'
      LabNames       = @('JWT100D4')
      IsGroup        = $false
      SamAccountName = 'JWT100D4_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Jack White Building JWT100D6 #>
    @{
      GUID           = '7089c4de-7095-4694-9bb3-c4b13f8369e3'
      Department     = 'Interurban, Jack White Building'
      LabNames       = @('JWT100D6')
      IsGroup        = $false
      SamAccountName = 'JWT100D6_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Portable A  PORTA104 #>
    @{
      GUID           = '3b591da2-c20b-4bac-b6f1-8f8f510744fa'
      Department     = 'Interurban, Portable A '
      LabNames       = @('PORTA104')
      IsGroup        = $false
      SamAccountName = 'PORTA104_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Technologies Building  TB135 #>
    @{
      GUID           = '70828e28-0e4c-4fbd-a533-d48465e3de24'
      Department     = 'Interurban, Technologies Building '
      LabNames       = @('TB135')
      IsGroup        = $false
      SamAccountName = 'TB135_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Technologies Building  TB145 #>
    @{
      GUID           = '0511acca-8838-4596-9ad8-b8d9c7c5cc2d'
      Department     = 'Interurban, Technologies Building '
      LabNames       = @('TB145')
      IsGroup        = $false
      SamAccountName = 'TB145_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Technologies Building  TB147 #>
    @{
      GUID           = '1394af0d-2f72-4056-868b-ddfa20794e53'
      Department     = 'Interurban, Technologies Building '
      LabNames       = @('TB147')
      IsGroup        = $false
      SamAccountName = 'TB147_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Technologies Building  TB148 #>
    @{
      GUID           = '31c9099d-a4f8-495c-8fc7-7ba146fa709b'
      Department     = 'Interurban, Technologies Building '
      LabNames       = @('TB148')
      IsGroup        = $false
      SamAccountName = 'TB148_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Technologies Building  TB150 #>
    @{
      GUID           = 'b6c2135f-5477-4f4c-8fc2-43b34603124e'
      Department     = 'Interurban, Technologies Building '
      LabNames       = @('TB150')
      IsGroup        = $false
      SamAccountName = 'TB150_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Technologies Building  TB151 #>
    @{
      GUID           = 'f1c7b44e-bb46-4df8-b4ca-1c3002343902'
      Department     = 'Interurban, Technologies Building '
      LabNames       = @('TB151')
      IsGroup        = $false
      SamAccountName = 'TB151_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Technologies Building  TB257 #>
    @{
      GUID           = '550dd39c-6b2d-4d27-808e-52ad68d0ed47'
      Department     = 'Interurban, Technologies Building '
      LabNames       = @('TB257')
      IsGroup        = $false
      SamAccountName = 'TB257_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Technologies Building  TB273 #>
    @{
      GUID           = '51b337f4-1d8b-448b-873f-15e5f552505d'
      Department     = 'Interurban, Technologies Building '
      LabNames       = @('TB273')
      IsGroup        = $false
      SamAccountName = 'TB273_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Technologies Building  TEC186A #>
    @{
      GUID           = '0e011057-b1cd-4495-928f-f8dd3211b399'
      Department     = 'Interurban, Technologies Building '
      LabNames       = @('TEC186A')
      IsGroup        = $false
      SamAccountName = 'TEC186A_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Technologies Building  TEC186E #>
    @{
      GUID           = 'fc7fa7a6-df7f-499c-ab6a-dcae85ccd8fb'
      Department     = 'Interurban, Technologies Building '
      LabNames       = @('TEC186E')
      IsGroup        = $false
      SamAccountName = 'TEC186E_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Technologies Building  TEC204A #>
    @{
      GUID           = '4496cd29-7a7a-4e6d-8e0a-8b62a89bf60a'
      Department     = 'Interurban, Technologies Building '
      LabNames       = @('TEC204A')
      IsGroup        = $false
      SamAccountName = 'TEC204A_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Technologies Building  TEC205A #>
    @{
      GUID           = '986c6d83-8072-46f6-8c45-9bace558c34f'
      Department     = 'Interurban, Technologies Building '
      LabNames       = @('TEC205A')
      IsGroup        = $false
      SamAccountName = 'TEC205A_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Technologies Building  TEC227A #>
    @{
      GUID           = '4225ef9b-be33-4258-bce5-6d00b7780a06'
      Department     = 'Interurban, Technologies Building '
      LabNames       = @('TEC227A')
      IsGroup        = $false
      SamAccountName = 'TEC227A_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Technologies Building  TEC229A #>
    @{
      GUID           = 'fb8825ee-e47f-49f0-99e2-138ce1a11fbf'
      Department     = 'Interurban, Technologies Building '
      LabNames       = @('TEC229A')
      IsGroup        = $false
      SamAccountName = 'TEC229A_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
    <# Interurban, Technologies Building  TEC259A #>
    @{
      GUID           = '8d89dd23-f051-4943-9207-910a0c83923f'
      Department     = 'Interurban, Technologies Building '
      LabNames       = @('TEC259A')
      IsGroup        = $false
      SamAccountName = 'TEC259A_devices'
      OutputFile     = ''
      SiteApp        = 'MyLabAccess.camosun.bc.ca'
      SiteRoot       = 'wwwroot\Student'
    }#>
  )
}
