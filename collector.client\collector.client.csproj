<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <Authors><PERSON></Authors>
    <Company>Camosun College</Company>
    <AssemblyTitle>collector.client</AssemblyTitle>
    <VersionPrefix>1.0.0</VersionPrefix>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <OutputType>Exe</OutputType>
    <AnalysisMode>Default</AnalysisMode>
    <EnableNETAnalyzers>true</EnableNETAnalyzers>
    <!-- <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild> -->
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <PublishDir>./publish</PublishDir>
    <PublishSingleFile>true</PublishSingleFile>
    <!-- <PublishTrimmed>true</PublishTrimmed> -->
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <OutputType>Exe</OutputType>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CLAP-dotnetcore" Version="4.6.4" />
    <PackageReference Include="NetMQ" Version="4.0.1.15" />
    <PackageReference Include="Serilog" Version="4.3.0" />
    <PackageReference Include="Serilog.Sinks.Async" Version="2.1.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
    <PackageReference Include="Serilog.Sinks.File.Archive" Version="1.0.6" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\collector.common\collector.common.csproj" />
  </ItemGroup>

</Project>
