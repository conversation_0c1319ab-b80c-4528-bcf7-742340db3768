<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CLAP-dotnetcore" Version="4.6.4" />
    <PackageReference Include="NetMQ" Version="4.0.1.15" />
    <PackageReference Include="Serilog" Version="4.3.0" />
    <PackageReference Include="Serilog.Sinks.Async" Version="2.1.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
    <PackageReference Include="Serilog.Sinks.File.Archive" Version="1.0.6" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\collector.common\collector.common.csproj" />
  </ItemGroup>

</Project>
