using System.Security.Principal;

using Microsoft.AspNetCore.Authorization;

namespace collector.webapp.Handlers;

[System.Runtime.Versioning.SupportedOSPlatform("windows")]
public class MemberOfHandler(ILogger<MemberOfHandler> logger) : AuthorizationHandler<MemberOfRequirement>
{
  private readonly ILogger<MemberOfHandler> _logger = logger;

  protected override Task HandleRequirementAsync(
  AuthorizationHandlerContext context,
  MemberOfRequirement requirement
)
  {
    // convert user to windows identity
    if (context.User.Identity is WindowsIdentity winId)
    {
      _logger.LogInformation("  + Name [{winId}] ", winId.Name);
      _logger.LogInformation("  + RoleClaimType [{winId}] ", winId.RoleClaimType);

      foreach (var item in requirement.Groups)
        _logger.LogInformation("  ! Require [{item}] ", item);

      // enumerate groups, if any
      if (
        null != winId.Groups &&
        winId.Groups.Count > 0
      )
      {
        _logger.LogInformation("  + Groups  [{winId}]", winId.Groups.Count);
        HashSet<string> userGroups = [];
        foreach (var group in winId.Groups)
        {
          var groupString = group.Translate(typeof(NTAccount)).ToString();
          userGroups.Add(groupString);
          if (requirement.Groups.Contains(groupString))
            _logger.LogInformation("  + Group  [{groupString}]", groupString);
        }

        var success = requirement.Groups.Overlaps(userGroups);
        _logger.LogInformation("  ? Success  [{success}]", success);

        // compare requirements to groups
        if (success)
          context.Succeed(requirement);
      }
    }

    return Task.CompletedTask;
  }
}
