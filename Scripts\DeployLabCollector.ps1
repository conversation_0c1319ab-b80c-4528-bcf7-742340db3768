<#
  .NOTES
  Collector Server
    mongodb (DeployMSI)
    mongodb PSModule
    files (LabCollector/Server) [site]
    collector.server service
    Firewall Rule?
    proxycollector tasks (Setup) [credentials]
    checkrdp tasks (Setup) [credentials?]

  Collector Client GPO
    Firewall Rule for collector.client
    Immediate tasks (Setup, Remove)
    ?

  Collector Client DSC
    Files (LabCollector/Client)
    Registration (Depends on Collector. Server fully running)
    Setup
    Heartbeat LastRun before now
    Build LabCollector
    dotnet publish -c Release

  Generate LabData files for each site
    Copy LabCollector Server files
    Copy LabCollector Client files

  Collector might need a 'ping':'pong' function before attempting to start/connect/register

#>

[CmdletBinding()]
param (
  # Splat for DSC source Path
  [Parameter()]
  [hashtable]
  $DSCSourcePath = @{ Path = '\\naslan.intra.camosun.bc.ca\DSCSources' },
  # Configuration Output Path
  [Parameter()]
  [hashtable]
  # Local output directory; Hashtable: single key [Path]
  $OutputPath = @{ Path = 'C:\Scripts' },
  # Remove Switch
  # [Parameter(ParameterSetName='Remove')]
  [switch]
  $Remove,

  # Client operation Switch
  [Parameter(ParameterSetName = 'Client', Mandatory)]
  [switch]
  $Client,
  # Site to serve
  [Parameter(ParameterSetName = 'Client', Mandatory)]
  [ValidateSet('None', 'Interurban', 'Lansdowne', 'Prod', 'Dev')]
  [string]
  $Site,
  # Force LCM
  [Parameter()]
  [switch]
  $Force
)

Import-Module -Name $PSScriptRoot/PackageManifest.psm1

function CheckLCM {
  param(
    [switch]$Force
  )
  if ($Force) {
    Write-Host '--- Not checking LCM State'
    return
  }
  Write-Host '--- Checking LCM State'
  $waitCounter = 0
  do {
    $LCMState = (Get-DscLocalConfigurationManager).LCMState
    $NotIdle = $LCMState -ne 'Idle'
    if ($NotIdle) {
      $waitTime = (Get-Random -Minimum 15 -Maximum 60)
      Write-Host ('--- [{2}:{0,3}] Waiting [{1,3}] seconds' -f ++$waitCounter, $waitTime, $LCMState)
      Start-Sleep -Seconds $waitTime
    }
  } while ($NotIdle)
}

try {
  # Get start time
  $StartTime = [datetime]::now

  # Ensure Output Path exists
  if (!(Test-Path @OutputPath -PathType Container)) {
    New-Item @OutputPath -ItemType Directory -Force
  }

  # Handle Transcript
  $TranscriptFile = @{
    Path = (Join-Path @OutputPath -ChildPath 'DeployLabCollector-Transcript.log' )
  }

  if (Test-Path @TranscriptFile -PathType Leaf) {
    $today = Get-Date -Format 'yyyyMMdd'
    $LastLogged = Get-Date -Format 'yyyyMMdd' (Get-ChildItem @TranscriptFile).LastWriteTime

    if ($today -eq $LastLogged) {
      #If we're still on the same day, append
      Start-Transcript @TranscriptFile -Append
    } else {
      # Otherwise start over.
      Start-Transcript @TranscriptFile
    }
  } else {
    # Otherwise start over.
    Start-Transcript @TranscriptFile
  }

  # Log start time
  Write-Host "--- StartTime [$StartTime]"
  Write-Host "--- SetupType [$SetupType]"
  $SourceName = if ($Site -in 'Dev', 'None') {
    'LabCollectorDev'
  } else {
    'LabCollector'
  }

  # DSC Folder
  $DSCOutputRoot = @{ Path = Join-Path @OutputPath -ChildPath 'DSC' }

  $LabCollectorLocalPath = @{ Path = Join-Path @OutputPath -ChildPath 'LabCollector' }
  $LabCollectorSourcePath = @{ Path = Join-Path @DSCSourcePath -ChildPath $SourceName }

  # Set Ensure State for DSC's
  $Ensure = if ($Remove) {
    'Absent'
  } else {
    'Present'
  }

  switch ($PSCmdlet.ParameterSetName) {
    'Client' {
      # Firewall Rules should be handled in the GPO?
      # Build DSC to do the rest
      $DestClientPath = @{ Path = Join-Path @LabCollectorLocalPath -ChildPath 'Client' }
      $SourceClientPath = @{ Path = Join-Path @LabCollectorSourcePath -ChildPath 'Client' }

      Write-Verbose -Message ('DestClientPath : <{0}>' -f ($DestClientPath | ConvertTo-Json -Compress))
      Write-Verbose -Message ('SourceClientPath : <{0}>' -f ($SourceClientPath | ConvertTo-Json -Compress))

      # If the directory doesn't exist, create it.
      if (!(Test-Path @DestClientPath -PathType Container)) {
        New-Item @DestClientPath -ItemType Directory
      }

      # Check that WSMAN Max envelope size is set correctly for DSC?
      $mes = Get-Item -Path WSMan:\localhost\MaxEnvelopeSizeKb
      if($mes.Value -lt 1000) {
        $UpdateMaxSize = @{
          Name    = 'maxEnvelopeSize'
          Path    = 'HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\WSMAN\Client'
          Value   = 8192
          Force   = $true
          Verbose = $true
        }
        Set-ItemProperty @UpdateMaxSize
      }

      # Client DSC Goes here
      Configuration DeployCollectorClient {
        param(
          [ValidateSet('Absent', 'Present')]
          [string]$Ensure,
          [hashtable]$DestClientPath,
          [hashtable]$SourceClientPath,
          [string]$Site
        )

        Import-DscResource -ModuleName 'PSDesiredStateConfiguration'

        Node localhost {
          if ($Ensure -eq 'Present') {
            # Copy all of the files to the local system for use.
            File ClientDirectory {
              DestinationPath = $DestClientPath['Path']
              Force           = $true
              Type            = 'Directory'
              Ensure          = 'Present'
            }

            $Manifests = @{
              SrcManifest = Get-Manifest @SourceClientPath
              DstManifest = Get-Manifest @DestClientPath -Exclusions Logs
            }

            $Changes = Get-ManifestChanges @Manifests

            $ChangesDependsOn = @{}
            $ChangeItems = $Changes.Adds + $Changes.Updates

            foreach ($Key in $ChangeItems.Keys) {
              $state_name = '[File]ClientPresent_{0}' -f $Key
              $Item = $ChangeItems[$Key]

              File $state_name {
                DestinationPath = (Join-Path @DestClientPath -ChildPath $Item)
                SourcePath      = (Join-Path @SourceClientPath -ChildPath $Key)
                Type            = 'File'
                Checksum        = 'SHA-256'
                Force           = $true
                Ensure          = 'Present'
                DependsOn       = '[File]ClientDirectory'
              }

              $ChangesDependsOn[$state_name] = $Item
            }

            foreach ($Item in $Changes.Removes) {
              $state_name = '[File]ClientAbsent_{0}' -f $Item

              File $state_name {
                DestinationPath = (Join-Path @DestClientPath -ChildPath $Item)
                Type            = 'File'
                Force           = $true
                Ensure          = 'Absent'
                DependsOn       = '[File]ClientDirectory'
              }

              $ChangesDependsOn[$state_name] = $Item
            }

            # Ensure the manifest is present, as it's not handled above
            $manifest_state = ('Manifest_{0}_{1}' -f $Manifests.SrcManifest.Name, $Manifests.SrcManifest.Version)
            File $manifest_state {
              DestinationPath = (Join-Path @DestClientPath -ChildPath 'manifest.json')
              SourcePath      = (Join-Path @SourceClientPath -ChildPath 'manifest.json')
              Type            = 'File'
              CheckSum        = 'SHA-256'
              # MatchSource     = $true
              Force           = $true
              Ensure          = 'Present'
              DependsOn       = (
                '[File]ClientDirectory',
                ($ChangesDependsOn.Keys)
              )
            }

            Script RegisterCollectorClient {
              TestScript = {
                (& 'C:\Scripts\LabCollector\Client\CollectorWrapper.ps1' -Test 'Registration')
              }
              SetScript  = {
                (& 'C:\Scripts\LabCollector\Client\CollectorWrapper.ps1' -Register -Site $using:Site)
              }
              GetScript  = {
                $Config = (Import-PowerShellDataFile 'C:\Scripts\LabCollector\Client\CollectorWrapper.cfg.psd1')['Sites'][$using:Site]
                @{ Results = ('Config: <{0}>' -f ($Config | ConvertTo-Json -Compress)) }
              }
              DependsOn  = @(
                ("[File]$manifest_state")
                '[File]ClientDirectory'
              )
            }

            Script SetupCollectorClient {
              TestScript = {
                (& 'C:\Scripts\LabCollector\Client\CollectorWrapper.ps1' -Test 'Setup')
              }
              SetScript  = {
                (& 'C:\Scripts\LabCollector\Client\CollectorWrapper.ps1' -Setup -Site $using:Site)
              }
              GetScript  = {
                $Config = (Import-PowerShellDataFile 'C:\Scripts\LabCollector\Client\CollectorWrapper.cfg.psd1')['Sites'][$using:Site]
                @{ Results = ('Config: <{0}>' -f ($Config | ConvertTo-Json -Compress)) }
              }
              DependsOn  = @(
                # '[File]ClientFiles'
                '[Script]RegisterCollectorClient'
              )
            }
          } else {
            Script SetupCollectorClient {
              TestScript = {
                if (Test-Path -Path 'C:\Scripts\LabCollector\Client\CollectorWrapper.ps1' -PathType Leaf) {
                  (& 'C:\Scripts\LabCollector\Client\CollectorWrapper.ps1' -Test 'Setup' -Remove)
                } else {
                  $true
                }
              }
              SetScript  = {
                (& 'C:\Scripts\LabCollector\Client\CollectorWrapper.ps1' -Setup -Site $using:Site -Remove)
              }
              GetScript  = {
                @{ Results = ('Config: < Removed: {0}>' -f ($using:Site)) }
              }
            }

            Script RegisterCollectorClient {
              TestScript = {
                if (Test-Path -Path 'C:\Scripts\LabCollector\Client\CollectorWrapper.ps1' -PathType Leaf) {
                  (& 'C:\Scripts\LabCollector\Client\CollectorWrapper.ps1' -Test 'Registration' -Remove)
                } else {
                  $true
                }
              }
              SetScript  = {
                (& 'C:\Scripts\LabCollector\Client\CollectorWrapper.ps1' -Register -Site $using:Site -Remove)
              }
              GetScript  = {

                @{ Results = ('Config: < Removed: {0}>' -f ($using:Site)) }
              }
              DependsOn  = @(
                '[Script]SetupCollectorClient'
              )
            }

            File ClientFiles {
              DestinationPath = $DestClientPath['Path']
              SourcePath      = $SourceClientPath['Path']
              Checksum        = 'SHA-256'
              Force           = $true
              Recurse         = $true
              type            = 'Directory'
              MatchSource     = $true
              Ensure          = $Ensure
              DependsOn       = @(
                '[Script]SetupCollectorClient'
                '[Script]RegisterCollectorClient'
              )
            }
          }
        }
      }

      $ClientWrapperState = @{
        OutputPath       = (Join-Path @DSCOutputRoot -ChildPath 'DeployCollectorClient')
        Ensure           = $Ensure
        DestClientPath   = $DestClientPath
        SourceClientPath = $SourceClientPath
        Site             = $Site
      }

      Write-Host '--- Compile DSC [DeployCollectorClient]'

      DeployCollectorClient @ClientWrapperState

      # $LCMOptions = @{
      #   Path  = $ClientWrapperState['OutputPath']
      #   Force = $true
      # }

      # Write-Host '--- Setting LCM'

      # Set-DscLocalConfigurationManager @LCMOptions

      $InvokeCfg = @{
        Path         = $ClientWrapperState['OutputPath']
        ComputerName = 'localhost'
        Wait         = $true
        Force        = $true
      }

      CheckLCM -Force:$Force

      Write-Host '--- Invoke DSC [DeployCollectorClient]'

      Start-DscConfiguration @InvokeCfg
    }
    Default {
      Write-Warning "--- SetupType [$SetupType] not defined."
    }
  }
} catch {
  throw $_
} finally {
  Write-Host "--- StopTime [$([datetime]::Now)]"
  Stop-Transcript
}
