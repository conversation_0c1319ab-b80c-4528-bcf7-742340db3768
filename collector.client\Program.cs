﻿using System.Net;
using System.Text;
using System.Text.Json;

using CLAP;

using collector.common.model;

using NetMQ;
using NetMQ.Sockets;

using Serilog;
using Serilog.Sinks.File.Archive;

namespace collector.client;

class Program
{
  public static TimeSpan timeout = TimeSpan.FromMilliseconds(common.Collector.MaxClientWaitTimeMS);

  [Empty, Help]
  public static void Help(string help) =>
    Log.Information(help);

  // Test
  [Verb]
  public static void Test(
    [Required, Description("DNS Name of the server you wish to connect to.")]
    string serverName
  )
  {
    TestData testData = new()
    {
      SomeData = $"[{Dns.GetHostName()} : {DateTimeOffset.Now.ToLocalTime()} : {common.Collector.Version}]"
    };

    SendData(serverName, testData);
  }

  // Register
  [Verb]
  public static void Register(
    [Required, Description("DNS Name of server you wish to connect to.")]
      string serverName,
    [Required, Description("Registration JSON Data to Send")]
      Registration data,
    [Description("OutFile for 'client.json', defaults to same directory as collector")]
      string outFile
  )
  {
    var subHeader = "[Register] ";
    // Check to see we're actually sending a registration
    // Issue with CLAP not deserializing the class correctly.
    var typeGuid = new Guid(Registration.GUID);
    if (data.Type_GUID == typeGuid)
    {
      // Connect to ZMQ socket
      string serverString = $"tcp://{serverName}:{common.Collector.RequestPort}";
      using var reqSocket = new RequestSocket();
      reqSocket.Connect(serverString);

      // Send the Registration Data
      reqSocket.SendFrame(data.ToString());
      // Process the response, should be a Client Object
      var succeeds = reqSocket.TryReceiveFrameString(timeout, out string? response);
      var IsClient = response?.Contains(Client.GUID.ToString()) ?? false;
      if (
        succeeds &&
        null != response &&
        IsClient
      )
      {
        //Convert it into a Client object and write it out
        var client = JsonSerializer.Deserialize<Client>(response);

        // Build file path for client.json
        var outPath = outFile ?? Path.Join(AppDomain.CurrentDomain.BaseDirectory, "client.json");

        if (
          null == client ||
          client.Client_GUID == common.Collector.DefaultGUID
        )
        {
          Log.Information($"{subHeader}Registration was unsuccessful, please ensure device [{data.HostName}] is in a registered lab group.");
          // Remove the client.json file if it exists.
          File.Delete(outPath);
        }
        else
        {
          Log.Information($"{subHeader}Registration was successful.");
          // Write the client.json file out.
          using var fs = File.OpenWrite(outPath);
          var bytesOut = new UTF8Encoding(false).GetBytes(client.ToString());
          fs.Write(bytesOut, 0, bytesOut.Length);
        }
      }
      else
        Log.Information($"{subHeader}Error: Did not receive a valid response.");

      Log.Information($"{subHeader}Received < {response} >");
    }
    else
      Log.Information($"{subHeader}Error: Cannot send [{data.Type_GUID}] as [{typeGuid}]");

  }

  private static void SendData<T>(
    string serverName,
    T data
    ) where T : LabData, new()
  {
    var subHeader = "[SendData] ";

    // Check to see that the right method was called for the right type
    // It looks like CLAP is not handling serialization correctly.
    var dataType = new T();
    if (data.Type_GUID != dataType.Type_GUID)
    {
      Log.Information($"{subHeader}Error: Cannot send [{data.Type_GUID}] as [{dataType.Type_GUID}]");
      return;
      // throw new Exception(Header + subHeader + $"Error: Cannot send [{dataType.Type_GUID}] as [{Data.Type_GUID}]");
    }
    string serverString = $"tcp://{serverName}:{common.Collector.RequestPort}";
    using var reqSocket = new RequestSocket();
    reqSocket.Connect(serverString);
    Log.Information($"{subHeader}Sending < {data} >");

    var send_attempts = 0;
    var send_success = false;

    do
    {
      send_success = reqSocket.TrySendFrame(timeout, data.ToString());

      Log.Information($"{subHeader}Sends attempted < {++send_attempts}:{send_success} >");
      if (!send_success)
        Thread.Sleep(common.Collector.MaxClientWaitTimeMS);

    } while (!send_success && send_attempts <= common.Collector.MaxClientAttempts);
    // reqSocket.SendFrame(Data.ToString());

    if (send_success)
    {
      string? response;
      var receive_success = false;
      var receive_attempts = 0;

      do
      {
        receive_success = reqSocket.TryReceiveFrameString(timeout, out response);

        Log.Information($"{subHeader}Receives attempted < {++receive_attempts}:{receive_success} >");
        if (!receive_success)
          Thread.Sleep(common.Collector.MaxClientWaitTimeMS);

      } while (!receive_success && receive_attempts <= common.Collector.MaxClientAttempts);

      if (receive_success)
        Log.Information($"{subHeader}Received < {response} >");
      else
        Log.Information($"{subHeader}Attempted [Receives: {receive_attempts}] and did not receive a response.");
    }
    else
      Log.Information($"{subHeader}Attempted [Sends: {send_attempts}] and did not receive a response.");
  }

  // Wrappers for SendData for CLAP to parse object correctly
  // Update
  [Verb]
  public static void UserSession(
    [Required, Description("DNS Name of server you wish to connect to.")]
      string serverName,
    [Required, Description("JSON Data to Send")]
      UserSession data
  ) => SendData(serverName, data);

  // Update
  [Verb]
  public static void Lab(
    [Required, Description("DNS Name of server you wish to connect to.")]
      string serverName,
    [Required, Description("JSON Data to Send")]
      Lab data
  ) => SendData(serverName, data);

  // Update
  [Verb]
  public static void Schedule(
    [Required, Description("DNS Name of server you wish to connect to.")]
      string serverName,
    [Required, Description("JSON Data to Send")]
      Schedule data
  ) => SendData(serverName, data);

  static void Main(string[] args)
  {
    // Setup logging
    Log.Logger = new LoggerConfiguration()
      .Enrich.FromLogContext()
      .WriteTo.Console()
      .WriteTo.Async(
        file => file.File(
          path: $"{AppDomain.CurrentDomain.BaseDirectory}/Logs/LabCollector/collector.client.log",
          rollingInterval: RollingInterval.Day,
          retainedFileCountLimit: 2,
          hooks: new ArchiveHooks(System.IO.Compression.CompressionLevel.Optimal)
        )
      )
      .CreateLogger();

    //Log.Information("Hello World!");
    // Console.Write($" === Collector.Client < ");
    try
    {
      Log.Information($"=== Collector.Client [{common.Collector.Version}] ===");

      foreach (var item in args)
        Log.Information($"{item} ");

      Log.Information("===");
      Parser.Run<Program>(args);
    }
    catch (Exception ex)
    {
      Log.Fatal(ex, "=== Client error ===");
    }
    finally
    {
      Log.CloseAndFlush();
    }
  }
}
