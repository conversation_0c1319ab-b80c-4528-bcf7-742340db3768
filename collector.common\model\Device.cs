using System.Text.Json;

namespace collector.common.model;

public class Device : IEquatable<Device>
{
  // Properties
  public string HostName { get; set; }
  public string DNSHostName { get; set; }
  public Guid Client_GUID { get; set; }

  // Methods
  public override string ToString() => JsonSerializer.Serialize(this);

  public bool Equals(Device? device)
  {
    return null != device &&
      HostName == device.HostName &&
      Client_GUID == device.Client_GUID;
  }

  public override bool Equals(object? obj) => Equals(obj as Device);
  public override int GetHashCode() => (HostName, Client_GUID).GetHashCode();

  // public int GetHashCode(Device device)
  // {
  //   if (null == device) return 0;

  //   int HostName_HC = device.HostName.GetHashCode();
  //   int ObjectGUID_HC = device.ObjectGUID.GetHashCode();
  //   return HostName_HC ^ ObjectGUID_HC;
  // }
}
