using System.Text.Json;

namespace collector.common.model;

public enum DataType
{
  <PERSON>ce,
  Client,
  Lab,
  Registration
}

public class GetData : LabData
{
  public const string GUID = "e47c040f-0442-42ab-b332-f4ca0de4ff9c";
  public override Guid Type_GUID { get; set; } = new Guid(GUID);

  public DataType DataTypeToRetrieve { get; set; } = DataType.Device;

  public string DataToRetrieve { get; set; }

  public override string ToString() => JsonSerializer.Serialize(this);
}
