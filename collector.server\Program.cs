using collector.server;

using Serilog;
using Serilog.Sinks.File.Archive;

// Configure Serilog
Log.Logger = new LoggerConfiguration()
  // Set minimum logging level
  .MinimumLevel.Information()
  .MinimumLevel.Override("Microsoft", Serilog.Events.LogEventLevel.Information)
  // Nice to have
  .Enrich.FromLogContext()
  // Set outputs
  .WriteTo.Console()
  .WriteTo.Async(
    f => f.File(
      // Daily roll-over, archiving & retaining the last seven days
      path: $"{AppDomain.CurrentDomain.BaseDirectory}/Logs/Listener/collector.server.log",
      rollingInterval: RollingInterval.Day,
      retainedFileCountLimit: 7,
      hooks: new ArchiveHooks(System.IO.Compression.CompressionLevel.Optimal)
    )
  )
  .CreateLogger();

// Build the Host application
IHost host = Host.CreateDefaultBuilder(args)
  // Set content root to where the exe is run
  .UseContentRoot(AppDomain.CurrentDomain.BaseDirectory)
  // Set Windows Service Options
  .UseWindowsService(options => options.ServiceName = ".NET Collector Listener")
  // Configure Host application services
  .ConfigureServices(
    (hostContext, services) =>
    {
      // Load configuration file
      var configuration = hostContext.Configuration;
      // Extract Listener Options
      ListenerOptions options = configuration.GetSection("Options").Get<ListenerOptions>() ?? new();
      // add Listener Options as a singleton
      services.AddSingleton(options);
      // Add the Listener Class as the Hosted service to run
      // Listener extends the BackgroundService class
      services.AddHostedService<Listener>();
    })
  // Setup Serilog for Logging
  .UseSerilog()
  .Build();

// Try to run the host application
try
{
  Log.Information($" === Starting LabCollector Server [{collector.common.Collector.Version}] ===");
  await host.RunAsync();
}
catch (Exception ex)
{
  Log.Fatal(ex, " === Server error ===");
}
finally
{
  Log.CloseAndFlush();
}
