<Project Sdk="Microsoft.NET.Sdk.Worker">

  <PropertyGroup>
    <Authors><PERSON></Authors>
    <Company>Camosun College</Company>
    <AssemblyTitle>collector.server</AssemblyTitle>
    <VersionPrefix>1.0.0</VersionPrefix>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <OutputType>Exe</OutputType>
    <UserSecretsId>dotnet-collector.server-6A02BAE0-D0F8-4340-A85C-11D1D55C4910</UserSecretsId>
    <AnalysisMode>Default</AnalysisMode>
    <EnableNETAnalyzers>true</EnableNETAnalyzers>
    <!-- <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild> -->
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <PublishDir>./publish</PublishDir>
    <PublishSingleFile>true</PublishSingleFile>
    <!-- <PublishTrimmed>true</PublishTrimmed> -->
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <OutputType>Exe</OutputType>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CLAP-dotnetcore" Version="4.6.4" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" Version="9.0.5" />
    <PackageReference Include="NetMQ" Version="********" />
    <PackageReference Include="Serilog" Version="4.3.0" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.Async" Version="2.1.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
    <PackageReference Include="Serilog.Sinks.File.Archive" Version="1.0.6" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\collector.common\collector.common.csproj" />
  </ItemGroup>
</Project>
