<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AnalysisMode>Default</AnalysisMode>
    <EnableNETAnalyzers>true</EnableNETAnalyzers>
    <!-- <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild> -->
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MongoDB.Driver" Version="3.4.0" />
    <PackageReference Include="System.Text.Json" Version="9.0.5" />
  </ItemGroup>

</Project>
