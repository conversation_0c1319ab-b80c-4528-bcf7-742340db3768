using System.Text.Json;

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace collector.common.model;

public class Lab : LabData
{
  public const string GUID = "620e8f12-6730-42eb-91e9-e6ed9ebfe6ce";
  public const string Default_Lab_GUID = "17a8b301-d86f-43eb-a5e8-9181400ac421";
  public override Guid Type_GUID { get; set; } = new Guid(GUID);

  [BsonId]
  public Guid Lab_GUID { get; set; }
  public string RoomName { get; set; }
  public string Campus { get; set; }
  public string? Building { get; set; }
  public string? Description { get; set; }
  public bool IsAdminOnly { get; set; }
  public bool IsStaffOnly { get; set; }
  public bool HasSchedule { get; set; }
  public bool Enabled { get; set; }

  [BsonRepresentation(BsonType.Document)]
  public DateTimeOffset LastUpdated { get; set; }
  public string? DeviceGroup { get; set; }
  public List<Device> Devices { get; set; } = [];
  public List<string> DefaultRDPGroups { get; set; } = [];

  public override string ToString() => JsonSerializer.Serialize(this);
}
