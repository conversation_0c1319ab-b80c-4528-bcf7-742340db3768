@startuml Registration
title Client Registration Process
entity Client
box Broker
control Router
queue Inbound
queue Outbound
control Dealer
end box
box Listener
entity Worker
end box
database MongoDB
' autoactivate on
  hnote over Client
    Registration call is made
    -ServerName (labcollector.camosun.ca)
    -Registration Data (register.json)
    -OutFile (client.json)
  end note
  Client o-> Router ++ : Open Request Socket connection
  Client -> Router : Send Registration Data
  Router -> Inbound -- : Receive message and queue it
  Inbound -> Dealer ++ : Dequeue the message
  Dealer -> Worker ++ : Take the Message
  Worker -> Worker : ProcessRegistration
  Worker -> MongoDB ++ : LabRead(Registration)
  alt Device doesn't exist
    MongoDB --> Worker : return null
      hnote right of Worker : Add Client to LabData (AD Device group)
    Worker -> Dealer : Empty Client from registration

  else Device Exists
    MongoDB --> Worker -- : Return device
      Worker -> MongoDB ++: ClientRead(device)
    alt Client doesnt't exist
      MongoDB --> Worker : return null
      hnote right of Worker : Client not found, race condition!
      Worker -> Dealer : Empty Client from registration
    else null == Client serial
      MongoDB --> Worker : return client
      hnote right of Worker : Client Registered!
      Worker -> MongoDB : Client updated from registration
      MongoDB -> Worker : return updated client
      Worker -> Dealer : Client returned
    else Registration serial != Client serial
      MongoDB --> Worker : return client
      hnote right of Worker : Prompted to unregister old client
      Worker -> Dealer : Empty Client from registration
    else Client already registered
      MongoDB --> Worker -- : return client
      hnote right of Worker : client already registered
      Worker -> Dealer : Client returned
    end
    Deactivate Worker
  end
  Dealer -> Outbound -- : Queue the message
  Outbound -> Router ++ : Dequeue the message
  Router -> Client -- : Return the response to the Client

  alt : Try Receive Frame
  hnote over Client
    The return Message is checked:
    - If it's an Empty Client (default guid of 0's), registration failed
    - Else registration succeeded and `client.json` is written
  end note
  Client o->x Router : Connection is closed.
@enduml
