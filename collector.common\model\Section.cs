using System.Text.Json;

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace collector.common.model;

public class Section
{
  public string? RoomName { get; set; }

  [BsonRepresentation(BsonType.Document)]
  public DateTimeOffset Start { get; set; }

  [BsonRepresentation(BsonType.Document)]
  public DateTimeOffset End { get; set; }
  public string? Group { get; set; }
  public List<string>? Users { get; set; }
  public override string ToString() => JsonSerializer.Serialize(this);
}
