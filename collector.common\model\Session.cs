using System.Text.Json;

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace collector.common.model;

public class Session //: ICloneable
{
  public string? UserName { get; set; }
  public string? SessionName { get; set; }
  public string? ID { get; set; }
  public string? State { get; set; }
  public string? IdleTime { get; set; }

  [BsonRepresentation(BsonType.Document)]
  public DateTimeOffset LogonTime { get; set; }

  public override string ToString() => JsonSerializer.Serialize(this);

  /* No longer used or needed.
  public Session Clone()
  {
    Session copy = (Session)this.MemberwiseClone();
    copy.UserName = string.Copy(this.UserName);
    copy.SessionName = string.Copy(this.SessionName);
    copy.ID = string.Copy(this.ID);
    copy.State = string.Copy(this.State);
    copy.IdleTime = string.Copy(this.IdleTime);
    copy.LogonTime = this.LogonTime;
    return copy;
  }
    */
  /*
  public object Clone()
  {
    return new Session
    {
      UserName = this.UserName,
      SessionName = this.SessionName,
      ID = this.ID,
      State = this.State,
      IdleTime = this.IdleTime,
      LogonTime = this.LogonTime,
    };
  }*/
}
