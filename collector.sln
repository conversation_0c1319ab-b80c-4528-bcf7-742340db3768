﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.30114.105
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "collector.common", "collector.common\collector.common.csproj", "{7B8C6797-41EF-4DAC-A1C3-E094D2D5DDF3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "collector.client", "collector.client\collector.client.csproj", "{6640167D-B883-4DB3-9E84-A00FC8CF3EAB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "collector.server", "collector.server\collector.server.csproj", "{2788AECD-B40C-49A1-9D42-263332C820EF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "collector.webapp", "collector.webapp\collector.webapp.csproj", "{A87CE2C4-169C-4070-9DE0-0B0FB359F70F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "collector.tester", "collector.tester\collector.tester.csproj", "{81776A90-6735-47D7-9F6C-F4E96C1F2585}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{7B8C6797-41EF-4DAC-A1C3-E094D2D5DDF3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7B8C6797-41EF-4DAC-A1C3-E094D2D5DDF3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7B8C6797-41EF-4DAC-A1C3-E094D2D5DDF3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7B8C6797-41EF-4DAC-A1C3-E094D2D5DDF3}.Release|Any CPU.Build.0 = Release|Any CPU
		{6640167D-B883-4DB3-9E84-A00FC8CF3EAB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6640167D-B883-4DB3-9E84-A00FC8CF3EAB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6640167D-B883-4DB3-9E84-A00FC8CF3EAB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6640167D-B883-4DB3-9E84-A00FC8CF3EAB}.Release|Any CPU.Build.0 = Release|Any CPU
		{2788AECD-B40C-49A1-9D42-263332C820EF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2788AECD-B40C-49A1-9D42-263332C820EF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2788AECD-B40C-49A1-9D42-263332C820EF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2788AECD-B40C-49A1-9D42-263332C820EF}.Release|Any CPU.Build.0 = Release|Any CPU
		{A87CE2C4-169C-4070-9DE0-0B0FB359F70F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A87CE2C4-169C-4070-9DE0-0B0FB359F70F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A87CE2C4-169C-4070-9DE0-0B0FB359F70F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A87CE2C4-169C-4070-9DE0-0B0FB359F70F}.Release|Any CPU.Build.0 = Release|Any CPU
		{81776A90-6735-47D7-9F6C-F4E96C1F2585}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{81776A90-6735-47D7-9F6C-F4E96C1F2585}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{81776A90-6735-47D7-9F6C-F4E96C1F2585}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{81776A90-6735-47D7-9F6C-F4E96C1F2585}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
EndGlobal
