@startuml "Check_RDPLogin Architecture"
participant Workstation
participant "JenkinsWin[int]"
participant softtsrv
participant webservices3

hnote over "JenkinsWin[int]" : Process LabData File
loop Foreach Lab in LabData
  loop Foreach Workstation in Lab
    "JenkinsWin[int]" -> "Workstation": quser /server:Workstation
    "Workstation" --> "JenkinsWin[int]": quser Session Data
  end
end
hnote over "JenkinsWin[int]", softtsrv : Generate HTML, RDP \nfiles from Session Data
"JenkinsWin[int]" -> softtsrv: RDP files are created on \nsofttsrv when generated
"JenkinsWin[int]" -> softtsrv: HTML files are updated \nafter everything else
softtsrv -> webservices3: HTML page has Lab \nRoom schedule lookup
hnote over softtsrv : Hosts: \nmylabaccess.camosun.bc.ca
@enduml