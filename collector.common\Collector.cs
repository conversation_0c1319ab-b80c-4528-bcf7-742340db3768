﻿namespace collector.common;

public static class Collector
{
  // Used for Clients (Connect) and Router (Bind)
  public const string RequestPort = "5555";
  // Used for Workers (Connect) and Dealer (Bind)
  public const string ResponsePort = "5556";
  public static Guid DefaultGUID = Guid.Empty;
  public const int MaxIntervalMinutes = 6;
  public const int MaxClientWaitTimeMS = 30 * 1000;
  public const int MaxBrokerWaitTimeMS = 100;
  public const int MaxClientAttempts = 10;
  public const string Version = "2.3.12";
}
