@startuml DeployServer
start
partition #LightBlue "Initialization" {
  fork
    partition #LightGreen "Server / Listener" {
      :Run: DeployServer.ps1 (as Administrator)
      -Server
      -Site 'None', 'Interurban', 'Lansdowne', 'Prod', 'Dev'
      -SetupTasks 'Features', 'Modules', 'Certificate', 'Scripts', 'Packages'
      -ProxyCredentials (Get-Secret 'LabCollectorAgent')
      -<PERSON>er<PERSON>e (OPTIONAL)
      -Remove (OPTIONAL)
      -Force (OPTIONAL)>
    }
  fork again
    partition #GoldenRod "Web Application" {
      :Run: DeployServer.ps1 (as Administrator)
      -WebApp
      -ServerMode 'Prod', 'Dev'
      -SetupTasks 'Features', 'Modules', 'Certificate', 'Scripts', 'Packages'
      -PfxCreds (Get-Secret 'GoDaddyWildCardPFX')
      -Ver<PERSON><PERSON> (OPTIONAL)
      -Remove (OPTIONAL)
      -Force (OPTIONAL)>
    }
  end fork

  :- DSCSourcePath[Path] = '\\naslan.intra.camosun.bc.ca\DSCSources'
  - OutputPath[Path] = 'C:\Scripts'
  - SetupTasks = @('Scripts')
  - StartTime = datetime::now |
  note
  Defaults
  end note
  if(Test-Path (C:\Scripts)?) then (no)
    :New-Item (C:\Scripts)>
  else (yes)
  endif
  if(Test-Path (TranscriptFile)) then (yes)
    :- Today = datetime::now as "yyyyMMdd"
    - LastLogged = TranscriptFile.LastWriteTime as "yyyyMMdd"|
    if( Today == LastLogged) then (yes)
      :Start-Transcript -Append>
    else (no)
      :Start-Transcript>
    endif
  else (no)
    :Start-Transcript -Append>
  endif
  :- LabCollectorLocalPath = Join-Path OutputPath, 'LabCollector'
  - LabCollectorSourcePath = Join-Path DSCSourcePath, 'LabCollector'|
  if(Remove) then (true)
    :Ensure = "Absent"|
  else (false)
    :Ensure = "Present"|
  endif
}
fork
  partition #LightGreen "Server / Listener" {
    :- FixedOrder = 'Packages', 'Modules', 'Scripts'
    - SetupTasks = FixedOrder.Where( SetupTasks == $_ )|
    note
    Sort SetupTasks
    end note
    if(null == ProxyCredentials) then (yes)
      :Get-Credential>
    else (no)
    endif
    switch(SetupTasks)
    case (Packages)
    :Run: DeployMSIPackage.ps1
    - PackageNames MongoDB
    - ForcedRestart
    - Verbose]
    case (Modules)
    :Commented out.
    Use SetupModules.ps1;
    case (Scripts)
    :Config (SetupProxyWrapper);
    endswitch
    :Continue;
  }
fork again
  partition #GoldenRod "Web Application" {

  }
end fork
stop
@enduml