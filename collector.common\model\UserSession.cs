using System.Text.Json;

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace collector.common.model;

public class UserSession : LabData
{
  public const string GUID = "59a24efa-a26c-485c-be83-3f6b66a28b5e";
  public override Guid Type_GUID { get; set; } = new Guid(GUID);
  public Guid Client_GUID { get; set; }

  [BsonRepresentation(BsonType.Document)]
  public DateTimeOffset SampleTime { get; set; }
  public string? ThisEvent { get; set; }
  public List<Session> Client_Sessions { get; set; } = [];

  public override string ToString() => JsonSerializer.Serialize(this);
}
