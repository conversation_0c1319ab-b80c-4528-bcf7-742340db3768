﻿using System.Collections.Concurrent;
using System.Net;
using System.Text;
using System.Text.Json;

using collector.common.model;
using collector.common.service;

using NetMQ;
using NetMQ.Sockets;

namespace collector.server;

public enum FrameType
{
  Dealer,
  Router
}

public class Listener(ILogger<Listener> logger, ListenerOptions options) : BackgroundService
{
  private readonly ILogger<Listener> _log = logger;
  private readonly ListenerOptions _options = options;

  // There's currently no way to run the NetMQRuntime async, so this blocks.
  // I may see about running it as a Singleton.
  protected override async Task ExecuteAsync(
  CancellationToken stoppingToken
)
  {
    var ListenerCount = _options.ListenerCount;
    var subHeader = "[Listen] ";
    _log.LogInformation("{subHeader}Starting [{ListenerCount}] Listeners, a Scanner, and a Broker.", subHeader, ListenerCount);

    // Start the NetMQ Runtime
    using var runtime = new NetMQRuntime();

    // Initialize Concurrent Queues for inbound and outbound work.
    ConcurrentQueue<NetMQMessage> DealerQueue = new();
    ConcurrentQueue<NetMQMessage> RouterQueue = new();

    List<Task> Jobs =
    [
      Broker(),
      // This doesn't behave correctly, very slow.
      // Create Dealer Socket
      // FrameBroker(
      //   $"tcp://*:{common.Collector.ResponsePort}",
      //   FrameType.Dealer,
      //   DealerQueue,
      //   RouterQueue
      // ),
      // Create Router Socket
      // FrameBroker(
      //   $"tcp://*:{common.Collector.RequestPort}",
      //   FrameType.Router,
      //   RouterQueue,
      //   DealerQueue
      // ),
      Scanner()
    ];

    // Add Listeners
    for (int i = 0; i < ListenerCount; i++)
    {
      Jobs.Add(ListenerWorker($"data{i}.json", i));
    }

    runtime.Run(stoppingToken, [.. Jobs]);
  }

  public async Task Scanner(
  // CancellationToken stoppingToken
  )
  {
    try
    {
      var subHeader = "[Scanner] ";
      var r = new Random();
      var counter = 1;
      do
      {
        _log.LogInformation("{subHeader}Client Offline scan started [{counter}]", subHeader, counter);
        var clients = await MongoDBService.ClientReadOffline();
        if (null != clients)
        {
          _log.LogInformation("{subHeader}Found [{Count,4:d4}] clients", subHeader, clients.Count);
          foreach (var client in clients)
            // Run the update on the client manually so that it does CalculateState();
            await MongoDBService.ClientUpdate(client);
        }
        await Task.Delay(TimeSpan.FromSeconds(30.0 + (r.NextDouble() * 60.0)));
        counter++;
        // reset the counter if we roll over 4 digits
        if (counter == 10000)
          counter = 0;
      } while (true);
    }
    catch (Exception ex)
    {
      _log.LogInformation("[Scanner] Exception! [{msg}]", ex.Message);

      throw;
    }
  }

  public async Task<bool> ProcessLab(Lab newLabData)
  {
    var subHeader = "[ProcessLab] ";

    // Get currentLabData using newLabData.
    var currentLabData = await MongoDBService.LabRead(newLabData);

    // If currentLabData doesn't exist, create it using the newLabData
    if (null == currentLabData)
    {
      // Lab doesn't exist in DB, create it
      _log.LogInformation("{subHeader}Lab Not Found, creating...", subHeader);

      if (await MongoDBService.LabCreate(newLabData))
        // Get currentLabData again
        currentLabData = await MongoDBService.LabRead(newLabData);
    }

    if (null != currentLabData)
      _log.LogInformation("{subHeader}Lab found [{Lab_GUID}]", subHeader, currentLabData.Lab_GUID);
    else
    {
      _log.LogInformation("{subHeader}Lab not found [{Lab_GUID}]", subHeader, newLabData.Lab_GUID);
      return false;
    }

    List<Device> adding = [];
    List<Device> keeping = [];
    List<Device> removing = [];

    // process adding, removing, and keeping
    switch ((newLabData.Devices.Count, currentLabData.Devices.Count))
    {
      // do nothing
      case (0, 0):
        break;

      // adding new devices
      case ( > 0, 0):
        // Devices not in Current Data, need to be created (if they don't exist) and linked.
        adding = [.. newLabData.Devices];
        break;

      // removing current devices
      case (0, > 0):
        // Devices not in New Data, need to be unlinked.
        removing = [.. currentLabData.Devices];
        break;

      // compare both for adding, removing, and keeping
      case ( > 0, > 0):
        // check and compare
        var currentDevices = currentLabData.Devices.ToList();
        var newDevices = newLabData.Devices.ToList();

        // Devices not in Current Data, need to be created (if they don't exist) and linked.
        adding = newDevices.Except(currentDevices).ToList();

        // Devices in both groups, not used, as these clients don't need to change.
        keeping = currentDevices.Intersect(newDevices).ToList();

        // Devices not in New Data, need to be unlinked.
        removing = currentDevices.Except(newDevices).ToList();
        break;

      default:
        break;
    }

    _log.LogInformation("{subHeader}Keep [{keeping}] Remove [{removing}] Add [{adding}]", subHeader, keeping.Count, removing.Count, adding.Count);

    // Unlink the Devices we're removing from Current Data
    if (removing.Count > 0)
    {
      foreach (var device in removing)
        await UnlinkClient(device, newLabData);
      // removed = true;
      _log.LogInformation("{subHeader}  Removed [{removing}]", subHeader, removing.Count);
    }

    // Create/Link the devices we're adding.
    if (adding.Count > 0)
    {
      foreach (var device in adding)
      {
        // See if the Client exists
        var client = await MongoDBService.ClientRead(device);
        // Create the new Client if doesn't exist
        if (null == client)
          await MongoDBService.ClientCreate(device);
        // and then Link it.
        await LinkClient(device, newLabData);
      }
      // added = true;
      _log.LogInformation("{subHeader}  Added   [{adding}]", subHeader, adding.Count);
    }

    // Do the same for keeping devices.
    if (keeping.Count > 0)
    {
      foreach (var device in keeping)
      {
        // See if the Client exists
        var client = await MongoDBService.ClientRead(device);
        // Create the new Client if doesn't exist
        if (null == client)
          await MongoDBService.ClientCreate(device);
        // and then Link it.
        await LinkClient(device, newLabData);
      }
      _log.LogInformation("{subHeader}  Kept    [{keeping}]", subHeader, keeping.Count);
    }

    //Write the new Lab document back
    var labResult = await MongoDBService.LabUpdate(newLabData);
    _log.LogInformation("{subHeader}Lab updated [{Lab_GUID}:{labResult}]", subHeader, newLabData.Lab_GUID, labResult);
    return labResult;
  }

  // Connects a Client (by device lookup) to a Lab
  public async Task LinkClient(Device device, Lab lab)
  {
    var subHeader = "[LinkClient] ";
    _log.LogInformation("{subHeader}Link device [{HostName}] to [{RoomName}]", subHeader, device.HostName, lab.RoomName);
    var client = await MongoDBService.ClientRead(device);
    if (null != client)
    {
      var update = false;

      // Check for HostName Changes
      if (
        client.HostName != device.HostName ||
        client.DNSHostName != device.DNSHostName
      )
      {
        client.HostName = device.HostName;
        client.DNSHostName = device.DNSHostName;
        update = true;
      }

      // Add the lab guid to the client if it doesn't exist
      if (!client.Lab_GUID.Contains(lab.Lab_GUID))
      {
        _log.LogInformation("{subHeader}Link client   [{HostName}:{Client_GUID}]", subHeader, client.HostName, client.Client_GUID);
        _log.LogInformation("{subHeader} ++  Lab_GUID [{RoomName}:{Lab_GUID}]", subHeader, lab.RoomName, lab.Lab_GUID);
        client.Lab_GUID.Add(lab.Lab_GUID);
        update = true;
      }

      // Apply updates
      if (update)
      {
        var result = await MongoDBService.ClientUpdate(client);
        _log.LogInformation("{subHeader}     Result   [{result}]", subHeader, result);
      }
    }
  }

  // Disconnects a Client (by device lookup) from a Lab
  public async Task UnlinkClient(Device device, Lab lab)
  {
    var subHeader = "[UnlinkClient] ";
    _log.LogInformation("{subHeader}Unlink device [{HostName}] to [{RoomName}]", subHeader, device.HostName, lab.RoomName);

    var client = await MongoDBService.ClientRead(device);
    if (
      null != client &&
      client.Lab_GUID.Contains(lab.Lab_GUID)
    )
    {
      _log.LogInformation("{subHeader}Unlink client [{HostName}:{Client_GUID}]", subHeader, client.HostName, client.Client_GUID);
      _log.LogInformation("{subHeader} --  Lab_GUID [{RoomName}:{Lab_GUID}]", subHeader, lab.RoomName, lab.Lab_GUID);
      client.Lab_GUID.Remove(lab.Lab_GUID);
      var result = await MongoDBService.ClientUpdate(client);
      _log.LogInformation("{subHeader}     Result   [{result}]", subHeader, result);
    }
  }

  public async Task Broker(
  // CancellationToken stoppingToken
  )
  {
    try
    {
      var subHeader = "[Broker] ";
      _log.LogInformation("{subHeader}Starting ...", subHeader);

      using var dealer = new DealerSocket();
      dealer.Bind($"tcp://*:{common.Collector.ResponsePort}");
      _log.LogInformation("{subHeader} ++ Dealer Socket [{ResponsePort}]", subHeader, common.Collector.ResponsePort);

      using var router = new RouterSocket();
      router.Bind($"tcp://*:{common.Collector.RequestPort}");
      _log.LogInformation("{subHeader} ++ Router Socket [{RequestPort}]", subHeader, common.Collector.RequestPort);

      ConcurrentQueue<NetMQMessage> InboundQueue = new();
      ConcurrentQueue<NetMQMessage> OutboundQueue = new();
      do
      {
        InboundQueue.Enqueue(await router.ReceiveMultipartMessageAsync());

        while (!InboundQueue.IsEmpty && InboundQueue.TryDequeue(out var netMQFrames))
          dealer.SendMultipartMessage(netMQFrames);

        OutboundQueue.Enqueue(await dealer.ReceiveMultipartMessageAsync());

        while (!OutboundQueue.IsEmpty && OutboundQueue.TryDequeue(out var netMQFrames))
          router.SendMultipartMessage(netMQFrames);

        await Task.Delay(10);
      } while (true);
    }
    catch (Exception ex)
    {
      _log.LogInformation("[Broker] Exception! [{msg}]", ex.Message);

      throw;
    }
  }

  public static object? GetFrameObject(string frame) => frame switch
  {
    _ when frame.Contains(Registration.GUID) => JsonSerializer.Deserialize<Registration>(frame),
    _ when frame.Contains(Client.GUID) => JsonSerializer.Deserialize<Client>(frame),
    _ when frame.Contains(UserSession.GUID) => JsonSerializer.Deserialize<UserSession>(frame),
    _ when frame.Contains(Lab.GUID) => JsonSerializer.Deserialize<Lab>(frame),
    _ when frame.Contains(Schedule.GUID) => JsonSerializer.Deserialize<Schedule>(frame),
    _ when frame.Contains(TestData.GUID) => JsonSerializer.Deserialize<TestData>(frame),
    _ when frame.Contains(GetData.GUID) => JsonSerializer.Deserialize<GetData>(frame),
    _ => "UnknownType"
  };

  public async Task<Client> ProcessRegistration(Registration registration)
  {
    var subHeader = "[ProcessingRegistration] ";
    _log.LogInformation("{subHeader}Processing: [{SerialNumber}]", subHeader, registration.SerialNumber);
    // Find the client using the registration.
    // The client will be identified by HostName first and then
    // by Serial number. It will either be:
    //  a) null --> Client was created by ProcessLab() and needs the serial number added.
    //  b) SerialNumber Matches --> Client device has been registered before and needs to restore client.json (reinstall of Collector.ClientCLI + wrapper)
    //  c) client has been replaced with a new system

    // There is a problem with this:
    // It should be looking at the LabData to get a device, and then using the device to lookup the client.
    // var client = await MongoDBService.ClientRead(registration.HostName);
    var device = await MongoDBService.LabRead(registration);

    if (null == device) // Device doesn't exist
    {
      _log.LogInformation("{subHeader}Device {HostName} not found in LabData, it needs to be in a lab device group before it can be registered.", subHeader, registration.HostName);

      // Return empty client
      var client = new Client(registration);
      _log.LogInformation("{subHeader}Returning empty guid [{HostName}:{Client_GUID}:{SerialNumber}]", subHeader, client.HostName, client.Client_GUID, client.SerialNumber);
      return client;
    }
    else // Device Exists
    {
      // Get the device, as it should exist if it's in the LabData
      var client = await MongoDBService.ClientRead(device);
      //  There's a possible race conditition here.
      //  If the Device exists, but hasn't been created by ProcessLab() yet, then this will return null.
      // So return an empty client
      if (null == client)
      {
        _log.LogInformation("{subHeader}Device {HostName} not found in ClientData, it needs to be created from a lab device group before it can be registered.", subHeader, registration.HostName);
        client = new Client(registration);
        _log.LogInformation("{subHeader}Returning empty guid [{HostName}:{Client_GUID}:{SerialNumber}]", subHeader, client.HostName, client.Client_GUID, client.SerialNumber);
      }
      else if (null == client.SerialNumber)
      { // Client was created via Lab, needs to finalize registration
        _log.LogInformation("{subHeader}Client exists, it will now be registered.", subHeader);
        // Register the serial number to the client.
        client.SerialNumber = registration.SerialNumber;
        // Store the changes
        await MongoDBService.ClientUpdate(client);
        // Read the new client back
        client = await MongoDBService.ClientRead(client);
        _log.LogInformation("{subHeader}Client now registered [{HostName}:{Client_GUID}:{SerialNumber}]", subHeader, client.HostName, client.Client_GUID, client.SerialNumber);
      }
      else if (registration.SerialNumber != client.SerialNumber)
      { // Old Client needs to be unregistered
        _log.LogInformation("{subHeader}Serial number does not match ['{registration}' != '{client}']", subHeader, registration.SerialNumber, client.SerialNumber);

        Registration unregister = new()
        {
          HostName = device.HostName,
          IsRegistering = false,
          SerialNumber = client.SerialNumber
        };
        _log.LogInformation("{subHeader}Please unregister <{unregister}>", subHeader, unregister);

        // Return empty client
        client = new Client(registration);
        _log.LogInformation("{subHeader}Returning empty guid [{HostName}:{Client_GUID}:{SerialNumber}]", subHeader, client.HostName, client.Client_GUID, client.SerialNumber);
      }
      else
        // Client exists and is registered
        _log.LogInformation("{subHeader}Client already registered [{HostName}:{Client_GUID}:{SerialNumber}]", subHeader, client.HostName, client.Client_GUID, client.SerialNumber);

      return client;
    }
  }

  // This should be invoked from a Unregister Task, after it has been unlinked from a lab.
  public async Task ProcessUnregistration(Registration registration)
  {
    var subHeader = "[ProcessingRegistration] ";
    _log.LogInformation("{subHeader}Processing: [{SerialNumber}]", subHeader, registration.SerialNumber);
    var client = await MongoDBService.ClientRead(registration);
    if (null == client)
      _log.LogInformation("{subHeader}Client Not Found, nothing to unregister.", subHeader);
    else
    {
      _log.LogInformation("{subHeader}Client found   [{HostName}:{Client_GUID}], unregistering...", subHeader, client.HostName, client.Client_GUID);
      await MongoDBService.ClientDelete(client);
      _log.LogInformation("{subHeader}Client deleted [{HostName}:{Client_GUID}]", subHeader, client.HostName, client.Client_GUID);
    }
  }

  public async Task<bool> ProcessUserSession(UserSession userSession)
  {
    var subHeader = "[ProcessingUserSession] ";
    _log.LogInformation("{subHeader}Processing: [{ThisEvent}:{Client_GUID}]", subHeader, userSession.ThisEvent, userSession.Client_GUID);
    // Find the matching client to update
    var client = await MongoDBService.ClientRead(userSession);
    // If it exists
    if (null != client)
    {
      _log.LogInformation("{subHeader}Client found [{HostName}:{Client_GUID}]", subHeader, client.HostName, client.Client_GUID);
      // Overwrite with current sessions.
      client.UserSessions = [.. userSession.Client_Sessions];

      //Update the event and sample time
      client.LastEvent = userSession.ThisEvent;
      client.LastUpdated = userSession.SampleTime;

      var result = await MongoDBService.ClientUpdate(client);
      return result;
    }
    else
    {
      _log.LogInformation("{subHeader}Client Not Found, please register [{Client_GUID}]", subHeader, userSession.Client_GUID);
      return false;
    }
  }

  public static async Task<bool> ProcessSchedule(Schedule schedule)
  {
    var scheduleDocument = await MongoDBService.ScheduleRead(schedule);
    return null == scheduleDocument
      ? await MongoDBService.ScheduleCreate(schedule)
      : await MongoDBService.ScheduleUpdate(schedule);
  }
  //[Verb]
  public async Task ListenerWorker(
    string outFile,
    int number
  // CancellationToken stoppingToken
  )
  {
    try
    {
      var subHeader = string.Format("[Worker : {0}]", number);
      _log.LogInformation("{subHeader} Starting Worker ", subHeader);
      // Setup ZMQ socket
      string serverString = $"tcp://localhost:{common.Collector.ResponsePort}";
      using var worker = new ResponseSocket();
      worker.Connect(serverString);

      do
      {
        // Going to need to use Option<T> here, maybe.
        // Listen for ZMQ Frames
        var (frame, more) = await worker.ReceiveFrameStringAsync();
        if (null == frame)
          continue;
        _log.LogInformation("{subHeader} Received < {frame}:{more} >", subHeader, frame, more);

        //Create an object from the frame based on Type_GUID
        object? frame_object = GetFrameObject(frame);

        // Process the object based on type
        switch (frame_object)
        {
          // Registration Type is processed by the MongoDBService Object. Returns a matching Client Object.
          case Registration registration:
            _log.LogInformation("{subHeader} Processing Registration < {SerialNumber} >", subHeader, registration.SerialNumber);
            if (registration.IsRegistering == true)
            {
              var returnClient = await ProcessRegistration(registration);
              worker.SendFrame(returnClient.ToString());
            }
            else
            {
              await ProcessUnregistration(registration);
              worker.SendFrame($"ACK: Unregistered <{registration.HostName} : {registration.SerialNumber}> [{DateTimeOffset.Now.ToLocalTime()}]");
            }
            break;

          // UserSession objects is processed by the MongoDBService Object. Returns bool for success in updating ClientData
          case UserSession userSession:
            _log.LogInformation("{subHeader} Processing UserSession < {Client_GUID} >", subHeader, userSession.Client_GUID);
            var sessionUpdated = await ProcessUserSession(userSession);
            worker.SendFrame($"ACK: Update <{userSession.Client_GUID}>:<{sessionUpdated}> [{DateTimeOffset.Now.ToLocalTime()}]");
            break;

          case Lab lab:
            _log.LogInformation("{subHeader} Processing Lab < {Lab_GUID} >", subHeader, lab.Lab_GUID);
            var labModified = await ProcessLab(lab);
            worker.SendFrame($"ACK: Update <{lab.Lab_GUID}>:<{labModified}> [{DateTimeOffset.Now.ToLocalTime()}]");
            break;

          case Client client:
            // Create the file to write to
            _log.LogInformation("{subHeader} Processing Client < {Client_GUID} >", subHeader, client.Client_GUID);
            var OutPath = Path.Join(Path.GetTempPath(), outFile);
            using (var fs = File.OpenWrite(OutPath))
            {
              var bytes_to_write = new UTF8Encoding(false).GetBytes(client.ToString());
              fs.Write(bytes_to_write, 0, bytes_to_write.Length);
            }
            /*if (Debug == true)
              {
                _log.LogInformation(frame_object.ToString());
              }*/
            worker.SendFrame($"ACK: [{DateTimeOffset.Now.ToLocalTime()}]");
            break;

          case Schedule schedule:
            _log.LogInformation("{subHeader} Processing Schedule < {Schedule_GUID} >", subHeader, schedule.Schedule_GUID);
            var scheduleUpdated = await ProcessSchedule(schedule);
            worker.SendFrame($"ACK: Schedule Updated <{schedule.Schedule_GUID}>:<{scheduleUpdated}> [{DateTimeOffset.Now.ToLocalTime()}]");
            break;

          case TestData testData:
            _log.LogInformation("{subHeader} Processing TestData < {testData} >", subHeader, testData);
            worker.SendFrame($"ACK: [{Dns.GetHostName()} : {DateTimeOffset.Now.ToLocalTime()} : {common.Collector.Version}]");
            break;

          case GetData getData:
            _log.LogInformation("{subHeader} Requested < {frame} >", subHeader, getData);
            try
            {
              var data = GetFrameObject(getData.DataToRetrieve);
              object? result = data switch
              {
                Device device => await MongoDBService.ClientRead(device),
                Client client => await MongoDBService.ClientRead(client),
                Lab lab => await MongoDBService.LabRead(lab),
                Registration registration => await MongoDBService.LabRead(registration),
                _ => "Unknown result type."
              };

              _log.LogInformation("{subHeader} Found < {frame} >", subHeader, result);
              worker.SendFrame($"ACK: Found [{DateTimeOffset.Now.ToLocalTime()}] : < {result} > ");
            }
            catch (Exception ex)
            {
              _log.LogError(ex, "Invalid object <{object}>", getData);
              worker.SendFrame($"ACK: Invalid object {frame_object} : < {frame} > [{DateTimeOffset.Now.ToLocalTime()}]");
            }
            break;

          // Handler for Unknowns
          default:
            _log.LogInformation("{subHeader} Unknown Type: < {frame} >", subHeader, frame);
            worker.SendFrame($"ACK: Unknown Type {frame_object} : < {frame} > [{DateTimeOffset.Now.ToLocalTime()}]");
            break;
        }

        // Wait a bit before checking again.
        // Don't know if we need this with await() above
        // Thread.Sleep(100);
      } while (true);
    }
    catch (Exception ex)
    {
      _log.LogInformation("[Worker] Exception! [{msg}]", ex.Message);

      throw;
    }
  }
}
