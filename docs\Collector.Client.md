# Collector.Client

## Command Line Parameters

### client: Help

```console
> collector.client

[12:59:26 INF] === Collector.Client [2.3.1] ===
[12:59:26 INF] ===
[12:59:26 INF]
   test|t
        /s /servername : DNS Name of the server you wish to connect to. (String) (Required)

   register|r
        /d /data       : Registration JSON Data to Send (Registration) (Required)
        /o /outfile    : OutFile for 'client.json', defaults to same directory as collector (String)
        /s /servername : DNS Name of server you wish to connect to. (String) (Required)

   usersession|u
        /d /data       : JSON Data to Send (UserSession) (Required)
        /s /servername : DNS Name of server you wish to connect to. (String) (Required)

   lab|l
        /d /data       : JSON Data to Send (Lab) (Required)
        /s /servername : DNS Name of server you wish to connect to. (String) (Required)

   schedule|s
        /d /data       : JSON Data to Send (Schedule) (Required)
        /s /servername : DNS Name of server you wish to connect to. (String) (Required)
```

### client: Test

> Note: This requires an active `collector.server` running on port 5555 (eg. `id2444v.intra.camosun.bc.ca`).

```console
> collector.client test -s='id2444v.intra.camosun.bc.ca'

[15:49:02 INF] === Collector.Client [2.3.1] ===
[15:49:02 INF] test
[15:49:02 INF] -s=id2444v.intra.camosun.bc.ca
[15:49:02 INF] ===
[15:49:02 INF] [SendData] Sending < {"Type_GUID":"cfd2001e-0143-45fb-bed7-ced645203045","SomeData":"[id2444l : 2022-06-07 3:49:02 p.m. -07:00 : 2.3.1]"} >
[15:49:02 INF] [SendData] Sends attempted < 1:True >
[15:49:03 INF] [SendData] Receives attempted < 1:True >
[15:49:03 INF] [SendData] Received < ACK: [ID2444V : 2022-06-07 15:49:02 -07:00 : 2.3.1] >
```

> Relevant server log lines:

```log
...
[15:49:02 INF] [Worker : 1] Received < {"Type_GUID":"cfd2001e-0143-45fb-bed7-ced645203045","SomeData":"[id2444l : 2022-06-07 3:49:02 p.m. -07:00 : 2.3.1]"}:False >
[15:49:02 INF] [Worker : 1] Processing TestData < {"Type_GUID":"cfd2001e-0143-45fb-bed7-ced645203045","SomeData":"[id2444l : 2022-06-07 3:49:02 p.m. -07:00 : 2.3.1]"} >
...
```

### client: Register

> Note:
>
> - This requires an active `collector.server` running on port 5555
> - The client system registering will need to be in a device group

The `registration` process works as follows:

```plantuml
@startuml Registration
title Client Registration Process
entity Client
box Broker
control Router
queue Inbound
queue Outbound
control Dealer
end box
box Listener
entity Worker
end box
database MongoDB
' autoactivate on
  hnote over Client
    Registration call is made
    -ServerName (labcollector.camosun.ca)
    -Registration Data (register.json)
    -OutFile (client.json)
  end note
  Client o-> Router ++ : Open Request Socket connection
  Client -> Router : Send Registration Data
  Router -> Inbound -- : Receive message and queue it
  Inbound -> Dealer ++ : Dequeue the message
  Dealer -> Worker ++ : Take the Message
  Worker -> Worker : ProcessRegistration
  Worker -> MongoDB ++ : LabRead(Registration)
  alt Device doesn't exist
    MongoDB --> Worker : return null
      hnote right of Worker : Add Client to LabData (AD Device group)
    Worker -> Dealer : Empty Client from registration

  else Device Exists
    MongoDB --> Worker -- : Return device
      Worker -> MongoDB ++: ClientRead(device)
    alt Client doesnt't exist
      MongoDB --> Worker : return null
      hnote right of Worker : Client not found, race condition!
      Worker -> Dealer : Empty Client from registration
    else null == Client serial
      MongoDB --> Worker : return client
      hnote right of Worker : Client Registered!
      Worker -> MongoDB : Client updated from registration
      MongoDB -> Worker : return updated client
      Worker -> Dealer : Client returned
    else Registration serial != Client serial
      MongoDB --> Worker : return client
      hnote right of Worker : Prompted to unregister old client
      Worker -> Dealer : Empty Client from registration
    else Client already registered
      MongoDB --> Worker -- : return client
      hnote right of Worker : client already registered
      Worker -> Dealer : Client returned
    end
    Deactivate Worker
  end
  Dealer -> Outbound -- : Queue the message
  Outbound -> Router ++ : Dequeue the message
  Router -> Client -- : Return the response to the Client

  alt : Try Receive Frame
  hnote over Client
    The return Message is checked:
    - If it's an Empty Client (default guid of 0's), registration failed
    - Else registration succeeded and `client.json` is written
  end note
  Client o->x Router : Connection is closed.
@enduml
```

> - If you attempt to register a client that is not in device group the following happens:

```console
> collector.client register -s='id2444v.intra.camosun.bc.ca' -d@='register.json' -o='client.json'

[16:33:28 INF] === Collector.Client [2.3.1] ===
[16:33:28 INF] register
[16:33:28 INF] -s=id2444v.intra.camosun.bc.ca
[16:33:28 INF] -d@=register.json
[16:33:28 INF] -o=client.json
[16:33:28 INF] ===
[16:33:29 INF] [Register] Registration was unsuccessful, please ensure device [id2444l] is in a registered lab group.
[16:33:29 INF] [Register] Received < {"Type_GUID":"17ff2b01-de2c-422f-8e50-35bd09840480","HostName":"id2444l","DNSHostName":null,"SerialNumber":"PC0JUL6L","CurrentState":0,"Client_GUID":"00000000-0000-0000-0000-000000000000","Server_GUID":"00000000-0000-0000-0000-000000000000","Lab_GUID":["17a8b301-d86f-43eb-a5e8-9181400ac421"],"LastUpdated":"0001-01-01T00:00:00+00:00","LastEvent":null,"UserSessions":null} >
```

> And on the server side:

```log
[16:33:28 INF] [Worker : 3] Received < {"Type_GUID":"2838835a-6b58-4d7e-b83d-7401aca79aee","HostName":"id2444l","SerialNumber":"PC0JUL6L","IsRegistering":true}:False >
[16:33:28 INF] [Worker : 3] Processing Registration < PC0JUL6L >
[16:33:28 INF] [ProcessingRegistration] Processing: [PC0JUL6L]
[16:33:28 INF] [ProcessingRegistration] Device id2444l not found in LabData, it needs to be in a lab device group before it can be registered.
[16:33:28 INF] [ProcessingRegistration] Returning empty guid [id2444l:00000000-0000-0000-0000-000000000000:PC0JUL6L]
```

> Add the device to a registered device group, and run/wait for the ProxyWrapper to update the server.
> The relevant lines in the server log during ProxyWrapper update:

```log
[16:36:25 INF] [Worker : 1] Received < {"Type_GUID":"620e8f12-6730-42eb-91e9-e6ed9ebfe6ce","Lab_GUID":"792a33fe-3f20-4660-aebe-34caa91a2fe7","RoomName":"Test","Campus":"None","Building":"None","Description":"Test Group","IsAdminOnly":true,"IsStaffOnly":false,"HasSchedule":false,"Enabled":true,"LastUpdated":"2022-06-07T16:36:24.9940575-07:00","DeviceGroup":"TestStations","Devices":[{"HostName":"ADMINVMINT","DNSHostName":"adminvmint.intra.camosun.bc.ca","Client_GUID":"a254212d-c325-482d-85ba-a4b7532d2beb"},{"HostName":"ADMINVMLAN","DNSHostName":"adminvmlan.intra.camosun.bc.ca","Client_GUID":"6d8ce4e9-e97b-4945-95ee-33bab525f3ab"},{"HostName":"ID2444L","DNSHostName":"ID2444L.intra.camosun.bc.ca","Client_GUID":"6a415542-f671-4e65-bb70-7155c3084f1a"},{"HostName":"ID2444V","DNSHostName":"ID2444V.intra.camosun.bc.ca","Client_GUID":"*************-4649-a349-56a73c7ea763"},{"HostName":"PAUL-TESTVM","DNSHostName":"paul-testvm.intra.camosun.bc.ca","Client_GUID":"1e3f963e-8183-4456-b4a4-da654476ddb5"}],"DefaultRDPGroups":["None"]}:False >
[16:36:25 INF] [Worker : 1] Processing Lab < 792a33fe-3f20-4660-aebe-34caa91a2fe7 >
[16:36:25 INF] [ProcessLab] Lab found [792a33fe-3f20-4660-aebe-34caa91a2fe7]
[16:36:25 INF] [ProcessLab] Checking new lab data for changes [792a33fe-3f20-4660-aebe-34caa91a2fe7]
[16:36:25 INF] [LinkClient] Link device [ID2444L] to [Test]
[16:36:25 INF] [LinkClient] Link client   [ID2444L:6a415542-f671-4e65-bb70-7155c3084f1a]
[16:36:25 INF] [LinkClient]  ++  Lab_GUID [Test:792a33fe-3f20-4660-aebe-34caa91a2fe7]
[16:36:25 INF] [LinkClient]      Result   [True]
[16:36:25 INF] [LinkClient] Link device [ADMINVMINT] to [Test]
[16:36:25 INF] [LinkClient] Link device [ADMINVMLAN] to [Test]
[16:36:25 INF] [LinkClient] Link device [ID2444V] to [Test]
[16:36:25 INF] [LinkClient] Link device [PAUL-TESTVM] to [Test]
[16:36:25 INF] [ProcessLab] Keep [4] Remove [0] Add [1]
```

> The registration should now work correctly:

```console
> collector.client register -s='id2444v.intra.camosun.bc.ca' -d@='register.json' -o='client.json'

[16:42:22 INF] === Collector.Client [2.3.1] ===
[16:42:22 INF] register
[16:42:22 INF] -s=id2444v.intra.camosun.bc.ca
[16:42:22 INF] -d@=register.json
[16:42:22 INF] -o=client.json
[16:42:22 INF] ===
[16:42:23 INF] [Register] Registration was successful.
[16:42:23 INF] [Register] Received < {"Type_GUID":"17ff2b01-de2c-422f-8e50-35bd09840480","HostName":"ID2444L","DNSHostName":"ID2444L.intra.camosun.bc.ca","SerialNumber":"PC0JUL6L","CurrentState":0,"Client_GUID":"6a415542-f671-4e65-bb70-7155c3084f1a","Server_GUID":"00000000-0000-0000-0000-000000000000","Lab_GUID":["17a8b301-d86f-43eb-a5e8-9181400ac421","792a33fe-3f20-4660-aebe-34caa91a2fe7"],"LastUpdated":"0001-01-01T00:00:00+00:00","LastEvent":null,"UserSessions":null} >
```

> And on the server:

```log
[16:42:22 INF] [Worker : 3] Received < {"Type_GUID":"2838835a-6b58-4d7e-b83d-7401aca79aee","HostName":"ID2444L","SerialNumber":"PC0JUL6L","IsRegistering":true}:False >
[16:42:22 INF] [Worker : 3] Processing Registration < PC0JUL6L >
[16:42:22 INF] [ProcessingRegistration] Processing: [PC0JUL6L]
[16:42:22 INF] [ProcessingRegistration] Client exists, it will now be registered.
[16:42:22 INF] [ProcessingRegistration] Client now registered [ID2444L:6a415542-f671-4e65-bb70-7155c3084f1a:PC0JUL6L]
```

> Note: should it be that the HostName in `register.json` does not match the name in Active Directory exactly (case-sensitive!) it will fail to register.

File Contents (these will be compressed json in production):

- `register.json`:

```json
{
  "Type_GUID": "2838835a-6b58-4d7e-b83d-7401aca79aee",
  "HostName": "ID2444L",
  "SerialNumber": "PC0JUL6L",
  "IsRegistering": true
}
```

- `client.json`

```json
{
  "Type_GUID": "17ff2b01-de2c-422f-8e50-35bd09840480",
  "HostName": "ID2444L",
  "DNSHostName": "ID2444L.intra.camosun.bc.ca",
  "SerialNumber": "PC0JUL6L",
  "CurrentState": 0,
  "Client_GUID": "6a415542-f671-4e65-bb70-7155c3084f1a",
  "Server_GUID": "00000000-0000-0000-0000-000000000000",
  "Lab_GUID": [
    "17a8b301-d86f-43eb-a5e8-9181400ac421",
    "792a33fe-3f20-4660-aebe-34caa91a2fe7"
  ],
  "LastUpdated": "0001-01-01T00:00:00+00:00",
  "LastEvent": null,
  "UserSessions": null
}
```

### client: UserSession/Lab/Schedule

Most clients will be running the `UserSession` variant, but they all work more or less the same. `Lab` and `Schedule` are used by the ProxyWrapper, but Schedule is unused at this point.

> Eg. TestStations, called from ProxyWrapper

```console
> collector.client lab -s='localhost' -d@='C:\Users\<USER>\Projects\labcollector\Scripts\LabData\Test-labdata.json'

[16:36:25 INF] === Collector.Client [2.3.0] ===
[16:36:25 INF] lab
[16:36:25 INF] -s=localhost
[16:36:25 INF] -d@=C:\Users\<USER>\Projects\labcollector\Scripts\LabData\Test-labdata.json
[16:36:25 INF] ===
[16:36:25 INF] [SendData] Sending < {"Type_GUID":"620e8f12-6730-42eb-91e9-e6ed9ebfe6ce","Lab_GUID":"792a33fe-3f20-4660-aebe-34caa91a2fe7","RoomName":"Test","Campus":"None","Building":"None","Description":"Test Group","IsAdminOnly":true,"IsStaffOnly":false,"HasSchedule":false,"Enabled":true,"LastUpdated":"2022-06-07T16:36:24.9940575-07:00","DeviceGroup":"TestStations","Devices":[{"HostName":"ADMINVMINT","DNSHostName":"adminvmint.intra.camosun.bc.ca","Client_GUID":"a254212d-c325-482d-85ba-a4b7532d2beb"},{"HostName":"ADMINVMLAN","DNSHostName":"adminvmlan.intra.camosun.bc.ca","Client_GUID":"6d8ce4e9-e97b-4945-95ee-33bab525f3ab"},{"HostName":"ID2444L","DNSHostName":"ID2444L.intra.camosun.bc.ca","Client_GUID":"6a415542-f671-4e65-bb70-7155c3084f1a"},{"HostName":"ID2444V","DNSHostName":"ID2444V.intra.camosun.bc.ca","Client_GUID":"*************-4649-a349-56a73c7ea763"},{"HostName":"PAUL-TESTVM","DNSHostName":"paul-testvm.intra.camosun.bc.ca","Client_GUID":"1e3f963e-8183-4456-b4a4-da654476ddb5"}],"DefaultRDPGroups":["None"]} >
[16:36:25 INF] [SendData] Sends attempted < 1:True >
[16:36:25 INF] [SendData] Receives attempted < 1:True >
[16:36:25 INF] [SendData] Received < ACK: Update <792a33fe-3f20-4660-aebe-34caa91a2fe7>:<True> [2022-06-07 16:36:25 -07:00] >
```

> And on the server, when a device is added:

```log
[16:36:25 INF] [Worker : 1] Received < {"Type_GUID":"620e8f12-6730-42eb-91e9-e6ed9ebfe6ce","Lab_GUID":"792a33fe-3f20-4660-aebe-34caa91a2fe7","RoomName":"Test","Campus":"None","Building":"None","Description":"Test Group","IsAdminOnly":true,"IsStaffOnly":false,"HasSchedule":false,"Enabled":true,"LastUpdated":"2022-06-07T16:36:24.9940575-07:00","DeviceGroup":"TestStations","Devices":[{"HostName":"ADMINVMINT","DNSHostName":"adminvmint.intra.camosun.bc.ca","Client_GUID":"a254212d-c325-482d-85ba-a4b7532d2beb"},{"HostName":"ADMINVMLAN","DNSHostName":"adminvmlan.intra.camosun.bc.ca","Client_GUID":"6d8ce4e9-e97b-4945-95ee-33bab525f3ab"},{"HostName":"ID2444L","DNSHostName":"ID2444L.intra.camosun.bc.ca","Client_GUID":"6a415542-f671-4e65-bb70-7155c3084f1a"},{"HostName":"ID2444V","DNSHostName":"ID2444V.intra.camosun.bc.ca","Client_GUID":"*************-4649-a349-56a73c7ea763"},{"HostName":"PAUL-TESTVM","DNSHostName":"paul-testvm.intra.camosun.bc.ca","Client_GUID":"1e3f963e-8183-4456-b4a4-da654476ddb5"}],"DefaultRDPGroups":["None"]}:False >
[16:36:25 INF] [Worker : 1] Processing Lab < 792a33fe-3f20-4660-aebe-34caa91a2fe7 >
[16:36:25 INF] [ProcessLab] Lab found [792a33fe-3f20-4660-aebe-34caa91a2fe7]
[16:36:25 INF] [ProcessLab] Checking new lab data for changes [792a33fe-3f20-4660-aebe-34caa91a2fe7]
[16:36:25 INF] [LinkClient] Link device [ID2444L] to [Test]
[16:36:25 INF] [LinkClient] Link client   [ID2444L:6a415542-f671-4e65-bb70-7155c3084f1a]
[16:36:25 INF] [LinkClient]  ++  Lab_GUID [Test:792a33fe-3f20-4660-aebe-34caa91a2fe7]
[16:36:25 INF] [LinkClient]      Result   [True]
[16:36:25 INF] [LinkClient] Link device [ADMINVMINT] to [Test]
[16:36:25 INF] [LinkClient] Link device [ADMINVMLAN] to [Test]
[16:36:25 INF] [LinkClient] Link device [ID2444V] to [Test]
[16:36:25 INF] [LinkClient] Link device [PAUL-TESTVM] to [Test]
[16:36:25 INF] [ProcessLab] Keep [4] Remove [0] Add [1]
```

> When a device is removed:

```log
[16:55:38 INF] [Worker : 3] Received < {"Type_GUID":"620e8f12-6730-42eb-91e9-e6ed9ebfe6ce","Lab_GUID":"792a33fe-3f20-4660-aebe-34caa91a2fe7","RoomName":"Test","Campus":"None","Building":"None","Description":"Test Group","IsAdminOnly":true,"IsStaffOnly":false,"HasSchedule":false,"Enabled":true,"LastUpdated":"2022-06-07T16:55:37.5239596-07:00","DeviceGroup":"TestStations","Devices":[{"HostName":"ADMINVMINT","DNSHostName":"adminvmint.intra.camosun.bc.ca","Client_GUID":"a254212d-c325-482d-85ba-a4b7532d2beb"},{"HostName":"ADMINVMLAN","DNSHostName":"adminvmlan.intra.camosun.bc.ca","Client_GUID":"6d8ce4e9-e97b-4945-95ee-33bab525f3ab"},{"HostName":"ID2444V","DNSHostName":"ID2444V.intra.camosun.bc.ca","Client_GUID":"*************-4649-a349-56a73c7ea763"},{"HostName":"PAUL-TESTVM","DNSHostName":"paul-testvm.intra.camosun.bc.ca","Client_GUID":"1e3f963e-8183-4456-b4a4-da654476ddb5"}],"DefaultRDPGroups":["None"]}:False >
[16:55:38 INF] [Worker : 3] Processing Lab < 792a33fe-3f20-4660-aebe-34caa91a2fe7 >
[16:55:38 INF] [ProcessLab] Lab found [792a33fe-3f20-4660-aebe-34caa91a2fe7]
[16:55:38 INF] [ProcessLab] Checking new lab data for changes [792a33fe-3f20-4660-aebe-34caa91a2fe7]
[16:55:38 INF] [UnlinkClient] Unlink device [ID2444L] to [Test]
[16:55:38 INF] [UnlinkClient] Unlink client [ID2444L:6a415542-f671-4e65-bb70-7155c3084f1a]
[16:55:38 INF] [UnlinkClient]  --  Lab_GUID [Test:792a33fe-3f20-4660-aebe-34caa91a2fe7]
[16:55:38 INF] [UnlinkClient]      Result   [True]
[16:55:38 INF] [LinkClient] Link device [ADMINVMINT] to [Test]
[16:55:38 INF] [LinkClient] Link device [ADMINVMLAN] to [Test]
[16:55:38 INF] [LinkClient] Link device [ID2444V] to [Test]
[16:55:38 INF] [LinkClient] Link device [PAUL-TESTVM] to [Test]
[16:55:38 INF] [ProcessLab] Keep [4] Remove [1] Add [0]
```

> When nothing changes:

```log
[16:57:20 INF] [Worker : 3] Received < {"Type_GUID":"620e8f12-6730-42eb-91e9-e6ed9ebfe6ce","Lab_GUID":"792a33fe-3f20-4660-aebe-34caa91a2fe7","RoomName":"Test","Campus":"None","Building":"None","Description":"Test Group","IsAdminOnly":true,"IsStaffOnly":false,"HasSchedule":false,"Enabled":true,"LastUpdated":"2022-06-07T16:57:19.7617023-07:00","DeviceGroup":"TestStations","Devices":[{"HostName":"ADMINVMINT","DNSHostName":"adminvmint.intra.camosun.bc.ca","Client_GUID":"a254212d-c325-482d-85ba-a4b7532d2beb"},{"HostName":"ADMINVMLAN","DNSHostName":"adminvmlan.intra.camosun.bc.ca","Client_GUID":"6d8ce4e9-e97b-4945-95ee-33bab525f3ab"},{"HostName":"ID2444V","DNSHostName":"ID2444V.intra.camosun.bc.ca","Client_GUID":"*************-4649-a349-56a73c7ea763"},{"HostName":"PAUL-TESTVM","DNSHostName":"paul-testvm.intra.camosun.bc.ca","Client_GUID":"1e3f963e-8183-4456-b4a4-da654476ddb5"}],"DefaultRDPGroups":["None"]}:False >
[16:57:20 INF] [Worker : 3] Processing Lab < 792a33fe-3f20-4660-aebe-34caa91a2fe7 >
[16:57:20 INF] [ProcessLab] Lab found [792a33fe-3f20-4660-aebe-34caa91a2fe7]
[16:57:20 INF] [ProcessLab] Checking new lab data for changes [792a33fe-3f20-4660-aebe-34caa91a2fe7]
[16:57:20 INF] [LinkClient] Link device [ADMINVMINT] to [Test]
[16:57:20 INF] [LinkClient] Link device [ADMINVMLAN] to [Test]
[16:57:20 INF] [LinkClient] Link device [ID2444V] to [Test]
[16:57:20 INF] [LinkClient] Link device [PAUL-TESTVM] to [Test]
[16:57:20 INF] [ProcessLab] Keep [4] Remove [0] Add [0]
```

## CollectorWrapper Script

TODO: This script handles a few things.

## Group Policy Objects

The group policy sets the following:

- Inbound and outbound firewall access rules for `%SystemDrive%\Scripts\LabCollector\Client\collector.client.exe`
- An "Immediate" scheduled task that calls: `\\naslan\DSCSources\LabCollector\Setup\DeployLabCollector.ps1 -Site <Prod or Dev> -Client -Verbose`
- Restarts the `WinRM` service as needed.

The GPO also provides an additional disabled "Immediate" scheduled task that can be used to __remove__ `LabCollector` across the board.
The scheduled task runs a script, `DeployLabCollector.ps1` that uses PowerShell DSC to perform installation and setup of the `collector.client` and related components.

```plantuml
@startuml DeployLabCollector.ps1
start
partition #LightBlue "Initialization" {
  :Initialize transcript directory and log file.
  Initialize additional variables.
  ]
}

partition #LightCoral "Client DSC" {
  fork
  : Ensure == 'Present' ]
  : File ClientDirectory >
  while (ForEach <File> in <DSCSources\LabCollector\Client\*>)
  : Script ClientFiles_<File> >
  endwhile
  : Script RegisterCollectorClient >
  : Script SetupCollectorClient >
  fork again
  : Ensure != 'Present' ]
  : Script SetupCollectorClient >
  : Script RegisterCollectorClient >
  : File ClientFiles >
  end fork
}

partition #LightSeaGreen "Apply DSC" {
  :Compile DSC;
  :Check Local Configuration Manager (LCM);
  :Start DSC Configuration;
}

:Stop Transcript>
stop
@enduml

```
