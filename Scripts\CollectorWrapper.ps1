<#
  .Synopsis
    Wrapper.ps1 provides an interface between Windows and collector
  .DESCRIPTION
    The Wrapper.ps1 script provides a means of setting up collector with Windows
    Scheduled Tasks.

    # Modes

    ## Register
    Uses config.psd1 to connect to a collector server and register Hostname and
    serial number (registration.json) for a client.json configuration file.

    ## Update
    Performs a QUser check and sends a data.json to the registered (client.json)
    collector server

    ## Heartbeat ?
    Scheduled to run every 5 minutes starting on the hour. Connects to collector
    server to check UsersSession against membership in Scheduled Section
    information. A user not in the Scheduled section will be notified 10 minutes
    and 5 minutes before the section starts and logged off when it starts.
    This mode will need to:
    - Send a logged in user a notification via Send-RDUserMessage
    - Logoff a logged in user via either logoff or Invoke-RDUserLogoff
    - Change memberships of local RDP group using:
      - Get-LocalGroup
      - Get-LocalGroupMember
      - Add-LocalGroupMember
      - Remove-LocalGroupMember

    ## Setup
    Creates all of the scheduled tasks that call the Update and Heartbeat modes

    ## Remove
    Removes all of the scheduled tasks created in Setup.

    ## Help or Default
    Displays help
  .Notes
    Wrapper modes

  Setup:
  - Generates the correct ScheduledTasks
  - Checks for a client.json
    - HostName = $env:COMPUTERNAME
    - MAC[]
    - Serial
    - Client_GUID
#>

[CmdletBinding(DefaultParameterSetName = 'Help')]
param(
  [Parameter(ParameterSetName = 'Register', Mandatory)]
  [switch]$Register,
  [Parameter(ParameterSetName = 'Register')]
  [string]$RegisterJSON = "$PSScriptRoot\register.json",
  [Parameter(ParameterSetName = 'Update')]
  [ValidateSet(
    'LogOffEvent',
    'Boot',
    'Logon',
    'RemoteConnect',
    'RemoteDisconnect',
    'SessionLock',
    'SessionUnlock',
    'Heartbeat',
    'EndOfDay'
  )]
  [string]$ThisEvent,
  [Parameter(ParameterSetName = 'Update')]
  [string]$UpdateJSON = "$PSScriptRoot\data.json",
  [Parameter(ParameterSetName = 'Setup', Mandatory)]
  [switch]$Setup,
  [Parameter(ParameterSetName = 'Setup')]
  [Parameter(ParameterSetName = 'Register')]
  [Parameter(ParameterSetName = 'Test')]
  [switch]$Remove,
  [Parameter(ParameterSetName = 'Help')]
  [switch]$Help,
  [Parameter(ParameterSetName = 'Test', Mandatory)]
  [ValidateSet('Setup', 'Registration')]
  [string[]]$Test,
  [string]$ClientJSON = "$PSScriptRoot\client.json",
  [string]$ConfigFile = "$PSscriptRoot\CollectorWrapper.cfg.psd1",
  # [ValidateSet('PROD', 'DEV')]
  # [string]$Mode = 'DEV',
  [Parameter(ParameterSetName = 'Setup', Mandatory)]
  [Parameter(ParameterSetName = 'Register', Mandatory)]
  [Parameter(ParameterSetName = 'Update', Mandatory)]
  [ValidateSet('None', 'Interurban', 'Lansdowne', 'Prod', 'Dev')]
  [string]$Site,
  # For logging
  [Parameter()]
  [hashtable]
  # Local output directory; Hashtable: single key [Path]
  $OutputPath = @{ Path = 'C:\Scripts' },
  [hashtable]$LogFile = @{ Path = "$PSScriptRoot\LabCollector-$(Get-Date -UFormat '%V').log" },
  [int]$LogsToKeep = 2,
  [string]$StartingTime = '07:00:00',
  [string]$EndingTime = '23:55:00',
  # For calling the actual Collector Client
  [string]$CollectorCommand = "$PSScriptRoot/collector.client"
)

#region DataTypes
#region LabData
class LabData {
  [guid]$Type_GUID
}
#endregion LabData

#region Session
class Session {
  [string]$UserName
  [string]$SessionName
  [string]$ID
  [string]$State
  [string]$IdleTime
  [DateTimeOffset]$LogonTime

  static [hashtable]$CSVOptions = @{
    Delimiter = ';'
    Header    = @(
      'UserName'
      'SessionName'
      'ID'
      'State'
      'IdleTime'
      'LogonTime'
    )
  }

  static [timespan]$MaxIdleTime = [timespan]'01:00:00'
  static [string]$NameTemplate = '{0}_{1}.session'
}
#endregion Session

#region Registration
class Registration : LabData {
  static [string]$GUID = '2838835a-6b58-4d7e-b83d-7401aca79aee'
  [GUID]$Type_GUID = [guid]::new([Registration]::GUID)
  [ValidateNotNullOrEmpty()]
  [string]$HostName
  [ValidateNotNullOrEmpty()]
  [string]$SerialNumber
  [bool]$IsRegistering
}
#endregion Registration
#region UserSession
class UserSession : LabData {
  static [string]$GUID = '59a24efa-a26c-485c-be83-3f6b66a28b5e'
  [GUID]$Type_GUID = [guid]([UserSession]::GUID)
  [Guid]$Client_GUID
  [DateTimeOffset]$SampleTime
  [string]$ThisEvent
  [Session[]]$Client_Sessions
}
#endregion UserSession
#endregion DataTypes

#region Functions
#region ShowHelp
function ShowHelp {
  param (
  )
  $Self = @{
    ScriptBlock = {
      help $PSCommandPath
    }
  }

  Invoke-Command @Self
}
#endregion ShowHelp

#region ConvertTaskToXML
function ConvertTaskToXML {
  param(
    [string]$Title,
    [string]$Type,
    [string]$Trigger,
    [string]$Parameters
  )
  $XML_Template = @"
<?xml version="1.0" encoding="UTF-16"?>
<Task version="1.4" xmlns="http://schemas.microsoft.com/windows/2004/02/mit/task">
<RegistrationInfo>
<Date>2020-07-27T11:18:43.5415287</Date>
<Author>CAMOSUN\heyp</Author>
<URI>\$Title $Type</URI>
</RegistrationInfo>
<Triggers>
$Trigger
</Triggers>
<Principals>
<Principal id="Author">
  <UserId>S-1-5-18</UserId>
  <RunLevel>HighestAvailable</RunLevel>
</Principal>
</Principals>
<Settings>
<MultipleInstancesPolicy>IgnoreNew</MultipleInstancesPolicy>
<DisallowStartIfOnBatteries>true</DisallowStartIfOnBatteries>
<StopIfGoingOnBatteries>true</StopIfGoingOnBatteries>
<AllowHardTerminate>true</AllowHardTerminate>
<StartWhenAvailable>false</StartWhenAvailable>
<RunOnlyIfNetworkAvailable>false</RunOnlyIfNetworkAvailable>
<IdleSettings>
  <StopOnIdleEnd>true</StopOnIdleEnd>
  <RestartOnIdle>false</RestartOnIdle>
</IdleSettings>
<AllowStartOnDemand>true</AllowStartOnDemand>
<Enabled>true</Enabled>
<Hidden>false</Hidden>
<RunOnlyIfIdle>false</RunOnlyIfIdle>
<DisallowStartOnRemoteAppSession>false</DisallowStartOnRemoteAppSession>
<UseUnifiedSchedulingEngine>true</UseUnifiedSchedulingEngine>
<WakeToRun>false</WakeToRun>
<ExecutionTimeLimit>PT10M</ExecutionTimeLimit>
<Priority>7</Priority>
</Settings>
<Actions Context="Author">
<Exec>
  <Command>powershell</Command>
  <Arguments>-NoLogo -NonInteractive -NoProfile -Command "&amp;{$PSCommandPath $Parameters}"</Arguments>
</Exec>
</Actions>
</Task>
"@
  return $XML_Template
}
#endregion ConvertTaskToXML

#region HeartBeatTimeCheck
function HeartBeatTimeCheck {
  param(
    [string]$TaskType
  )
  # Handler for starting Heartbeat if in the active time window
  # Watch this if end time gets adjusted to midnight.
  if ($TaskType -eq 'Heartbeat') {
    $TaskName = $MainTitle + ' ' + $TaskType
    $StartTime = Get-Date $StartingTime
    $EndTime = Get-Date $EndingTime
    $Now = Get-Date

    if (
      ($Now -ge $StartTime) -and
      ($Now -lt $EndTime)
    ) {
      Start-ScheduledTask $TaskName -ErrorAction SilentlyContinue
    } else {
      Stop-ScheduledTask $TaskName -ErrorAction SilentlyContinue
    }
  }
}
#endregion HeartBeatTimeCheck

#region Register-Client
function Register-Client {
  $count = 0
  do {
    $CimInstance = @{
      ClassName = 'Win32_BIOS'
      Property  = 'SerialNumber'
      KeyOnly   = $true
    }

    # Must match collector.Model.Registration
    $RegisterBlock = [ordered]@{
      Type_GUID     = '2838835a-6b58-4d7e-b83d-7401aca79aee'
      HostName      = $env:COMPUTERNAME
      SerialNumber  = (Get-CimInstance @CimInstance).SerialNumber
      IsRegistering = $true
    }
    Write-Host (' --- [{0, 3}]Registering with collector server.')
    $RegisterBlock |
      ConvertTo-Json -Compress |
      Out-File -Encoding utf8 -FilePath $RegisterJSON -Force
    #dotnet run
    #$Messages =
    Invoke-Expression @"
    $CollectorCommand register -s="$($Config['ServerName'])" -d@="$RegisterJSON" -o="$ClientJSON"
"@
    #Write-Host $Messages
    # Test the client
  }until(
    (Test-Path -Path $ClientJSON -PathType Leaf) -or
    ($count -eq 10)
  )
}
#endregion Register-Client

#region ConvertFrom-IdleTime
function ConvertFrom-IdleTime ([string]$IdleTime) {
  $span = switch -regex ($IdleTime) {
    '^none$' {
      @{
        Minutes = 0
      }
    }
    '^\.$' {
      @{
        Minutes = 0
      }
    }
    '^\d{1,2}$' {
      @{
        Minutes = $_
      }
    }
    '^\d{1,2}:\d{1,2}$' {
      $h, $m = $_ -split ':', 2
      @{
        Hours   = $h
        Minutes = $m
      }
    }
    '^\d+\+\d{1,2}:\d{1,2}$' {
      $d, $h, $m = $_ -replace '\+', ':' -split ':', 3
      @{
        Days    = $d
        Hours   = $h
        Minutes = $m
      }
    }
  }
  New-TimeSpan @span
}
#endregion ConvertFrom-IdleTime

#region Optimize-Sessions
function Optimize-Sessions {
  param (
    # Current UserSession to process
    [Parameter(Mandatory)]
    [UserSession]
    $UserSession,
    # Parameter help description
    [Parameter(Mandatory)]
    [string]
    $ThisEvent
  )
  <#
  .NOTES
    If there are no sessions, there should be no session files

    User state is either Active or Disc

    Disc: (for any event).
      If Session file exists, always remove it.
      Check if IdleTime is > Max idle time
        if it is, log the user off.

    Active:
    Event               Action?
    'SessionLock':      Create session file (forced)
    'SessionUnlock':    Remove session file
    'LogOffEvent'       There might be a session file for active
    'Boot'              Should be no sessions or files
    'Logon'             Should be no session files for active
    'RemoteConnect'     Should be no session files for active
    'RemoteDisconnect'  Should be no session files for active
    'Heartbeat':        There might be a session file for active
    'EndOfDay' :        There might be a session file for active

    Default on other events: if session file, check last write time
    Session files that don't match active, remove them.
  #>

  # Check for Session files
  Write-Host ('--- Optimize Sessions [{0}]' -f $UserSession.Client_Sessions.Count)

  $FileSearch = @{
    Path   = $PSScriptRoot
    Filter = '*.session'
    File   = $true
  }

  $RemoveOptions = @{
    Force   = $true
    Verbose = $true
  }

  $SessionFiles = Get-ChildItem @FileSearch

  if (!$UserSession.Client_Sessions) {
    # No actual client sessions, remove any session files.
    if ($SessionFiles) {
      Write-Host "  # No active sessions, remove all session files [$($sessionFiles.Name -join ', ')]"
      $SessionFiles | Remove-Item @RemoveOptions
    }

    # Return early
    return
  }

  Write-Host ('  # Found session files [{0}]' -f $SessionFiles.Count)

  # If sessions exist, process them
  foreach ($session in $UserSession.Client_Sessions) {
    # Generate some info
    # Name of the session file
    $sessionFileName = [Session]::NameTemplate -f $session.UserName, $session.ID

    # Splat for Removing & Creating
    $fileOptions = @{
      Path    = (Join-Path -Path $PSScriptRoot -ChildPath $sessionFileName)
      # Force   = $true
      Verbose = $true
    }

    # Check if a session file already exists
    $sessionExists = $SessionFiles.Name -contains $sessionFileName

    if ($session.State -eq 'Disc') {
      # If there's a session file, remove it
      # Handled in Active?
      if ($sessionExists) {
        Write-Host "  # Remove Disc session file [$sessionFileName]"
        Remove-Item @fileOptions
      }

      # If a disconnected session has been idle for more than the max idle time
      if ($session.IdleTime -gt [Session]::MaxIdleTime) {
        # Log it off
        Write-Host "  # Logoff Disc session [logoff $($session.ID)]"
        logoff $session.ID
      }
    }

    if ($session.State -eq 'Active') {
      # Enumerate any session files that don't match Active
      $ActiveSessionFile, $staleSessionFiles = $SessionFiles.Where(
        { $_.Name -eq $sessionFileName },
        'Split'
      )

      foreach ($file in $staleSessionFiles) {
        Write-Host "  # Remove stale session file [$($file.Name)]"
        $file | Remove-Item @RemoveOptions
      }

      # Process the event
      switch ($ThisEvent) {
        # Create session file (forced)
        'SessionLock' {
          # if (!$sessionExists) {
          Write-Host "  # Add session file [$sessionFileName]"
          New-Item @fileOptions
          # }
        }
        # Remove this session file
        'SessionUnlock' {
          if ($sessionExists) {
            Write-Host "  # Remove session file [$sessionFileName]"
            Remove-Item @fileOptions
          }
        }

        # All of these other events should be processed the same way
        # 'LogOffEvent' {}
        # 'Boot' {} # No sessions on boot
        # 'Logon' {}
        # 'RemoteConnect' {}
        # 'RemoteDisconnect' {}
        # 'Heartbeat' {}
        # 'EndOfDay' {}
        Default {
          # Check last write time of session file
          if (
            $sessionExists -and
            ([datetime]::Now - $ActiveSessionFile.LastWriteTime) -ge [Session]::MaxIdleTime
          ) {
            # Log it off
            Write-Host "  # Logoff locked session [logoff $($session.ID)]"
            logoff $session.ID
          }
        }
      }
    }

    # Check for changes to session files for next loop
    $SessionFiles = Get-ChildItem @FileSearch
  }
}
#endregion Optimize-Sessions

#endregion Functions

#region Global Variables
$MainTitle = 'Collector Client Wrapper'
$ScriptVersion = [version]'1.0.1'

$TaskTypes = @(
  @{
    Title      = $MainTitle
    Type       = 'LogOffEvent'
    Parameters = "-Site $Site -ThisEvent 'LogOffEvent'"
    Trigger    = @'
<EventTrigger>
<Enabled>true</Enabled>
<Subscription>&lt;QueryList&gt;&lt;Query Id="0" Path="Security"&gt;&lt;Select Path="Security"&gt;*[System[Provider[@Name='Microsoft-Windows-Security-Auditing'] and EventID=4647]]&lt;/Select&gt;&lt;/Query&gt;&lt;/QueryList&gt;</Subscription>
<Delay>PT5S</Delay>
</EventTrigger>
'@
  }
  @{
    Title      = $MainTitle
    Type       = 'Boot'
    Parameters = "-Site $Site -ThisEvent 'Boot'"
    Trigger    = @'
<BootTrigger>
  <Enabled>true</Enabled>
  <Delay>PT5S</Delay>
</BootTrigger>
'@
  }
  @{
    Title      = $MainTitle
    Type       = 'Logon'
    Parameters = "-Site $Site -ThisEvent 'Logon'"
    Trigger    = @'
<LogonTrigger>
  <Enabled>true</Enabled>
  <Delay>PT5S</Delay>
</LogonTrigger>
'@
  }
  @{
    Title      = $MainTitle
    Type       = 'RemoteConnect'
    Parameters = "-Site $Site -ThisEvent 'RemoteConnect'"
    Trigger    = @'
<SessionStateChangeTrigger>
  <Enabled>true</Enabled>
  <StateChange>RemoteConnect</StateChange>
  <Delay>PT5S</Delay>
</SessionStateChangeTrigger>
'@
  }
  @{
    Title      = $MainTitle
    Type       = 'RemoteDisconnect'
    Parameters = "-Site $Site -ThisEvent 'RemoteDisconnect'"
    Trigger    = @'
<SessionStateChangeTrigger>
  <Enabled>true</Enabled>
  <StateChange>RemoteDisconnect</StateChange>
  <Delay>PT5S</Delay>
</SessionStateChangeTrigger>
'@
  }
  @{
    Title      = $MainTitle
    Type       = 'SessionLock'
    Parameters = "-Site $Site -ThisEvent 'SessionLock'"
    Trigger    = @'
<SessionStateChangeTrigger>
  <Enabled>true</Enabled>
  <StateChange>SessionLock</StateChange>
  <Delay>PT5S</Delay>
</SessionStateChangeTrigger>
'@
  }
  @{
    Title      = $MainTitle
    Type       = 'SessionUnlock'
    Parameters = "-Site $Site -ThisEvent 'SessionUnlock'"
    Trigger    = @'
<SessionStateChangeTrigger>
  <Enabled>true</Enabled>
  <StateChange>SessionUnlock</StateChange>
  <Delay>PT5S</Delay>
</SessionStateChangeTrigger>
'@
  }
  @{
    Title      = $MainTitle
    Type       = 'Heartbeat'
    Parameters = "-Site $Site -ThisEvent 'Heartbeat'"
    Trigger    = @"
    <CalendarTrigger>
      <Repetition>
        <Interval>PT5M</Interval>
        <Duration>PT17H</Duration>
        <StopAtDurationEnd>true</StopAtDurationEnd>
      </Repetition>
      <StartBoundary>$(Get-Date -Format s $StartingTime)</StartBoundary>
      <Enabled>true</Enabled>
      <ScheduleByDay>
        <DaysInterval>1</DaysInterval>
      </ScheduleByDay>
    </CalendarTrigger>
"@
  }
  @{
    Title      = $MainTitle
    Type       = 'EndOfDay'
    Parameters = "-Site $Site -ThisEvent 'EndOfDay'"
    Trigger    = @"
    <CalendarTrigger>
      <StartBoundary>$(Get-Date -Format s $EndingTime)</StartBoundary>
      <Enabled>true</Enabled>
      <RandomDelay>PT10M</RandomDelay>
      <ScheduleByDay>
        <DaysInterval>1</DaysInterval>
      </ScheduleByDay>
    </CalendarTrigger>
"@
  }
  <## Not needed >
  @{
    Title     = $MainTitle
    Type      = 'StartOfDay'
    Parameters = "-Site $Site -ThisEvent 'StartOfDay'"
    Trigger   = @"
    <CalendarTrigger>
      <StartBoundary>$(Get-Date -Format s $StartingTime)</StartBoundary>
      <Enabled>true</Enabled>
      <RandomDelay>PT10M</RandomDelay>
      <ScheduleByDay>
        <DaysInterval>1</DaysInterval>
      </ScheduleByDay>
    </CalendarTrigger>
"@
  }#>
)
#endregion Global Variables

try {
  #region Start of Script
  Push-Location $PSScriptRoot
  # Get start time
  $StartTime = [datetime]::now

  # Process Tests before anything else
  if ($Test) {
    # Used for DSC
    $TestResults = @(
      Switch ($Test) {
        'Setup' {
          foreach ($Task in $TaskTypes) {
            $ScheduledTask = @{
              TaskName = "$($Task.Title + ' ' + $Task.Type)"
              TaskPath = '\'
            }

            try {
              Get-ScheduledTask @ScheduledTask -ErrorAction Stop
              $true
            } catch {
              $false
            }
          }
        }
        'Registration' {
          # Check for register.json
          $RegistrationJSONPath = @{ Path = (Join-Path -Path $PSScriptRoot -ChildPath 'register.json') }
          # Write-Verbose "--- RegistrationJSONPath: <$($RegistrationJSONPath|ConvertTo-Json -Compress)>"
          # Test 1.
          $RegistrationExists = Test-Path @RegistrationJSONPath -PathType Leaf

          # If it exists, get the content
          $RegistrationJSON = if ($RegistrationExists) {
            Get-Content @RegistrationJSONPath
          } else {
            $null
          }

          $CimInstance = @{
            ClassName = 'Win32_BIOS'
            Property  = 'SerialNumber'
            KeyOnly   = $true
          }

          # Must match collector.Model.Registration
          $RegisterBlock = [ordered]@{
            Type_GUID     = '2838835a-6b58-4d7e-b83d-7401aca79aee'
            HostName      = $env:COMPUTERNAME
            SerialNumber  = (Get-CimInstance @CimInstance).SerialNumber
            IsRegistering = $true
          }
          # Generate a registration to compare with, for Test 2.
          $GeneratedRegistrationJSON = $RegisterBlock | ConvertTo-Json -Compress

          # Check for client.json
          $ClientJSONPath = @{ Path = (Join-Path -Path $PSScriptRoot -ChildPath 'client.json') }
          $ClientExists = Test-Path @ClientJSONPath -PathType Leaf

          # Test 3.
          $ClientMatchesRegistration = if (
            $ClientExists -and
            $RegistrationExists
          ) {
            $Client = Get-Content @ClientJSONPath | ConvertFrom-Json
            $Registration = $RegistrationJSON | ConvertFrom-Json

            # return
            $Client.HostName -eq $Registration.HostName
          } else {
            $false
          }

          # return
          ($RegistrationExists) -and
          ($GeneratedRegistrationJSON -eq $RegistrationJSON) -and
          ($ClientMatchesRegistration)
        }
        Default {
          $false
        }
      })
    return !($Remove -in $TestResults)
  }

  # Ensure Output Path exists
  if (!(Test-Path @OutputPath -PathType Container)) {
    New-Item @OutputPath -ItemType Directory -Force
  }

  # Handle Transcript
  $TranscriptFile = @{
    Path = (Join-Path @OutputPath -ChildPath 'LabCollector-Transcript.log' )
  }

  if (Test-Path @TranscriptFile -PathType Leaf) {
    $today = Get-Date -Format 'yyyyMMdd'
    $LastLogged = Get-Date -Format 'yyyyMMdd' (Get-ChildItem @TranscriptFile).LastWriteTime

    if ($today -eq $LastLogged) {
      #If we're still on the same day, append
      Start-Transcript @TranscriptFile -Append
    } else {
      # Otherwise start over.
      Start-Transcript @TranscriptFile
    }
  } else {
    # Otherwise start over.
    Start-Transcript @TranscriptFile
  }

  # Log start time
  Write-Host "--- CollectorWrapper [$ScriptVersion]"
  Write-Host "--- StartTime [$StartTime]"

  # Import config
  $Config = (Import-PowerShellDataFile $ConfigFile)['Sites'][$Site]
  #endregion Start of Script

  #region Process options
  switch ($PSCmdlet.ParameterSetName) {
    'Register' {
      if ($Remove) {
        # Removing
        # Stop anything running
        Get-Process 'Collector.ClientCLI' -ErrorAction SilentlyContinue |
          Stop-Process -Force -ErrorAction SilentlyContinue

        # Clear the Tasks
        foreach ($Task in $TaskTypes) {
          try {
            $ScheduledTask = @{
              TaskName    = "$($Task.Title + ' ' + $Task.Type)"
              TaskPath    = '\'
              Verbose     = $true
              ErrorAction = 'SilentlyContinue'
            }
            if (Get-ScheduledTask -TaskName $ScheduledTask.TaskName -ErrorAction SilentlyContinue) {
              Unregister-ScheduledTask @ScheduledTask -Confirm:$false
              Write-Host "--- [$($ScheduledTask.TaskName)] now unregistered."
            } else {
              Write-Host "--- [$($ScheduledTask.TaskName)] not registered."
            }
          } catch {
            Write-Host "--- [$($Task.Title)] has an issue: < $_ >" -Level Warn
            continue
          }
        }

        # Need to clean up old data files.
        Get-ChildItem "$PSScriptRoot\*.json" | Remove-Item -Verbose -Force
      } else {
        # Installing
        $CimInstance = @{
          ClassName = 'Win32_BIOS'
          Property  = 'SerialNumber'
          KeyOnly   = $true
        }

        # Must match collector.Model.Registration
        $RegisterBlock = [Registration]@{
          # Type_GUID     = '2838835a-6b58-4d7e-b83d-7401aca79aee'
          HostName      = $env:COMPUTERNAME
          SerialNumber  = (Get-CimInstance @CimInstance).SerialNumber
          IsRegistering = $true
        }
        Write-Host '--- Registering with collector server.'
        $RegisterBlock |
          ConvertTo-Json -Compress |
          Out-File -Encoding utf8 -FilePath $RegisterJSON -Force
        #dotnet run
        #$Messages =
        Invoke-Expression @"
    $CollectorCommand register -s="$($Config['ServerName'])" -d@="$RegisterJSON" -o="$ClientJSON"
"@
        #Write-Host $Messages
        # Test the client
      }
    }

    'Update' {
      # Test client.json
      $ClientData = if ((Test-Path $ClientJSON -PathType Leaf)) {
        Get-Content $ClientJSON | ConvertFrom-Json
      } else {
        Write-Host "[$ClientJSON] not found, please run 'Wrapper.ps1 -Register'." -Level Warn
        exit
      }

      # Must match collector.Model.UserSession
      $UserSessionBlock = [UserSession]@{
        # Type_GUID       = '59a24efa-a26c-485c-be83-3f6b66a28b5e'
        Client_GUID     = $ClientData.Client_GUID
        SampleTime      = [DateTimeOffset]::Now
        ThisEvent       = $ThisEvent
        Client_Sessions = @()
      }

      # $s = [Session]@{}
      # $StringOptions = @{
      #   Delimiter     = ';'
      #   PropertyNames = $s.psobject.properties.name
      # }

      $CSVOptions = ([Session]::CSVOptions)
      $whoami = whoami

      $quser = (query user 2>$null)

      Write-Verbose "-- quser => {$quser}"

      if ($quser) {
        # If we have users
        # Generate the header for the hashtable
        $qheader = ($quser[0] -split '\s\s+').Trim()
        # Clean up spaces in names in header
        $qheader = $qheader -replace ' ', ''
        # Split the text into the user sessions
        $users = $quser[1..$($quser.Count)]
        # Generate the array of users
        $UserSessionBlock.Client_Sessions = @(
          foreach ($line in $users) {
            # Handle disconnected sessions
            if ($line -match 'Disc') {
              # need to replace empty session name with something. -- in this case
              $line = $line -replace '^(\s[\w\-]+\s\s)', '$1--'
            }
            # Split up the user lines with a marker and convert to hashtable
            $user = $line -replace '\s\s+', $CSVOptions['Delimiter'] |
              ConvertFrom-Csv @CSVOptions
            # This converts to an object inconsistently
            # ConvertFrom-String @StringOptions

            # Convert IdleTime '.' to 0 to be used as a TimeSpan
            $user.IDLETIME = ConvertFrom-IdleTime $user.IDLETIME

            # Clean up the leading character (' ' or '>') in the username field
            $user.USERNAME = $user.USERNAME -replace '^\W(.+)', '$1'

            Write-Verbose "-- user => {$($user | Out-String)}"
            # Return the hastable as an element of the array
            [Session]@{
              UserName    = $user.USERNAME
              SessionName = $user.SESSIONNAME
              ID          = $user.ID
              State       = $user.STATE
              IdleTime    = $user.IDLETIME
              LogonTime   = $user.LOGONTIME
            }
          }
        )
      }

      $Data = $UserSessionBlock | ConvertTo-Json -Depth 5 | Out-String
      Write-Host "    $ThisEvent as [$whoami] : < $Data >"
      Write-Host "--- Sending update [$ThisEvent]."
      $UserSessionBlock |
        ConvertTo-Json -Compress |
        Out-File -FilePath $UpdateJSON -Encoding utf8 -Force
      #dotnet run
      Invoke-Expression @"
    $CollectorCommand usersession -s="$($Config['ServerName'])" -d@="$UpdateJSON"
"@
      # Optimize the user sessions
      Optimize-Sessions -UserSession $UserSessionBlock -ThisEvent $ThisEvent
      # Check that the Heartbeat should be running or not.
      HeartBeatTimeCheck -TaskType $ThisEvent
    }

    'Setup' {
      if ($Remove) {
        # Removing
        # Stop anything running
        Get-Process 'Collector.ClientCLI' -ErrorAction SilentlyContinue |
          Stop-Process -Force -ErrorAction SilentlyContinue

        # Clear the Tasks
        foreach ($Task in $TaskTypes) {
          try {
            $ScheduledTask = @{
              TaskName    = "$($Task.Title + ' ' + $Task.Type)"
              TaskPath    = '\'
              Verbose     = $true
              ErrorAction = 'SilentlyContinue'
            }
            if (Get-ScheduledTask -TaskName $ScheduledTask.TaskName -ErrorAction SilentlyContinue) {
              Unregister-ScheduledTask @ScheduledTask -Confirm:$false
              Write-Host "--- [$($ScheduledTask.TaskName)] now unregistered."
            } else {
              Write-Host "--- [$($ScheduledTask.TaskName)] not registered."
            }
          } catch {
            Write-Host "--- [$($Task.Title)] has an issue: < $_ >" -Level Warn
            continue
          }
        }

        # Need to clean up old data files.
        Get-ChildItem "$PSScriptRoot\*.json" | Remove-Item -Verbose -Force
      } else {
        # Installing
        foreach ($Task in $TaskTypes) {
          try {
            $ScheduledTask = @{
              Xml      = ConvertTaskToXML @Task
              TaskName = "$($Task.Title + ' ' + $Task.Type)"
              TaskPath = '\'
              Verbose  = $true
            }
            #$xml = ConvertTaskToXML @Task
            #Register-ScheduledTask -Xml $xml -TaskName "$($Task.Title + ' ' + $Task.Type)" -Verbose
            if (Get-ScheduledTask -TaskName $ScheduledTask.TaskName -ErrorAction SilentlyContinue) {
              Write-Host "--- [$($ScheduledTask.TaskName)] already registered."
            } else {
              Register-ScheduledTask @ScheduledTask
              Write-Host "--- [$($ScheduledTask.TaskName)] now registered."
            }
            HeartBeatTimeCheck -TaskType $Task.Type
          } catch {
            Write-Host "--- [$($Task.Title)] has an issue: < $_ >" -Level Warn
            continue
          }
        }
      }
    }

    'Help' {
      ShowHelp
    }

    Default {
      ShowHelp
    }
  }
  #endregion Process options
} catch {
  throw $_
} finally {
  if (!$Test) {
    Write-Host "--- StopTime [$([datetime]::Now)]"
    Stop-Transcript
  }
  Pop-Location
}
