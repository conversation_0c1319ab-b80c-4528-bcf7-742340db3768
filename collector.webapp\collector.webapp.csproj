<Project Sdk="Microsoft.NET.Sdk.Web">

  <ItemGroup>
    <ProjectReference Include="..\collector.common\collector.common.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Negotiate" Version="8.0.4" />
    <PackageReference Include="Serilog" Version="4.3.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.Async" Version="2.1.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
    <PackageReference Include="Serilog.Sinks.File.Archive" Version="1.0.6" />
  </ItemGroup>

  <PropertyGroup>
    <AnalysisMode>Default</AnalysisMode>
    <EnableNETAnalyzers>true</EnableNETAnalyzers>
    <!-- <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild> -->
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

</Project>
