﻿using System.Net;

using CLAP;

using collector.common.model;

using NetMQ;
using NetMQ.Sockets;

using Serilog;
// using Serilog.Sinks.File.Archive;

namespace collector.tester;

class Program
{
  public static TimeSpan timeout = TimeSpan.FromMilliseconds(common.Collector.MaxClientWaitTimeMS);

  [Empty, Help]
  public static void Help(string help) =>
      Log.Information(help);

  // Test
  [Verb]
  public static void Test(
    [Required, Description("DNS Name of the server you wish to connect to.")]
    string serverName
  )
  {
    TestData testData = new()
    {
      SomeData = $"[{Dns.GetHostName()} : {DateTimeOffset.Now.ToLocalTime()} : {common.Collector.Version}]"
    };

    SendData(serverName, testData);
  }

  [Verb]
  public static void CheckData(
    [Required, Description("DNS Name of the server you wish to connect to.")]
    string serverName,
    [Required, Description("Type of data to request")]
    DataType dataType,
    [Required, Description("Object file for dataType")]
    string dataFile
  )
  {
    GetData getData = new()
    {
      DataTypeToRetrieve = dataType,
      DataToRetrieve = dataFile
    };

    SendData(serverName, getData);
  }

  private static void SendData<T>(
    string serverName,
    T data
  ) where T : LabData, new()
  {
    var subHeader = "[SendData] ";

    // Check to see that the right method was called for the right type
    // It looks like CLAP is not handling serialization correctly.
    var dataType = new T();
    if (data.Type_GUID != dataType.Type_GUID)
    {
      Log.Information($"{subHeader}Error: Cannot send [{data.Type_GUID}] as [{dataType.Type_GUID}]");
      return;
      // throw new Exception(Header + subHeader + $"Error: Cannot send [{dataType.Type_GUID}] as [{Data.Type_GUID}]");
    }
    string serverString = $"tcp://{serverName}:{common.Collector.RequestPort}";
    using var reqSocket = new RequestSocket();
    reqSocket.Connect(serverString);
    Log.Information($"{subHeader}Sending < {data} >");

    var send_attempts = 0;
    var send_success = false;

    do
    {
      send_success = reqSocket.TrySendFrame(timeout, data.ToString());

      Log.Information($"{subHeader}Sends attempted < {++send_attempts}:{send_success} >");
      if (!send_success)
        Thread.Sleep(common.Collector.MaxClientWaitTimeMS);

    } while (!send_success && send_attempts <= common.Collector.MaxClientAttempts);
    // reqSocket.SendFrame(Data.ToString());

    if (send_success)
    {
      string? response;
      var receive_success = false;
      var receive_attempts = 0;

      do
      {
        receive_success = reqSocket.TryReceiveFrameString(timeout, out response);

        Log.Information($"{subHeader}Receives attempted < {++receive_attempts}:{receive_success} >");
        if (!receive_success)
          Thread.Sleep(common.Collector.MaxClientWaitTimeMS);

      } while (!receive_success && receive_attempts <= common.Collector.MaxClientAttempts);

      if (receive_success)
        Log.Information($"{subHeader}Received < {response} >");
      else
        Log.Information($"{subHeader}Attempted [Receives: {receive_attempts}] and did not receive a response.");
    }
    else
      Log.Information($"{subHeader}Attempted [Sends: {send_attempts}] and did not receive a response.");
  }

  private static void Main(string[] args)
  {
    // Setup logging
    Log.Logger = new LoggerConfiguration()
      .Enrich.FromLogContext()
      .WriteTo.Console()
      .CreateLogger();

    //Log.Information("Hello World!");
    // Console.Write($" === Collector.Client < ");
    try
    {
      Log.Information($"=== Collector.Client [{common.Collector.Version}] ===");

      foreach (var item in args)
        Log.Information($"{item} ");

      Log.Information("===");
      Parser.Run<Program>(args);
    }
    catch (Exception ex)
    {
      Log.Fatal(ex, "=== Client error ===");
    }
    finally
    {
      Log.CloseAndFlush();
    }
  }
}
