using System.Text.Json;

namespace collector.common.model;

public class Registration : LabData
{
  public const string GUID = "2838835a-6b58-4d7e-b83d-7401aca79aee";
  public override Guid Type_GUID { get; set; } = new Guid(GUID);
  public string HostName { get; set; }
  public string SerialNumber { get; set; }
  public bool IsRegistering { get; set; }

  public override string ToString() => JsonSerializer.Serialize(this);
}
