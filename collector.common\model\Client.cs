using System.Text.Json;

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace collector.common.model;

public enum ThisEvent
{
  LogOffEvent,
  Boot,
  Logon,
  RemoteConnect,
  RemoteDisconnect,
  SessionLock,
  SessionUnlock,
  Heartbeat,
  EndOfDay
}

public enum DeviceState
{
  NotConnected,
  Available,
  InUse,
  Offline
}

public class Client : LabData
{
  public const string GUID = "17ff2b01-de2c-422f-8e50-35bd09840480";

  public override Guid Type_GUID { get; set; } = new Guid(GUID);
  // Properties
  public string HostName { get; set; }
  public string? DNSHostName { get; set; }
  //public string[] MAC { get; set; }
  public string? SerialNumber { get; set; }

  public DeviceState CurrentState { get; set; } = DeviceState.NotConnected;

  [BsonId]
  public Guid Client_GUID { get; set; }

  public Guid Server_GUID { get; set; }

  public List<Guid> Lab_GUID { get; set; }

  [BsonRepresentation(BsonType.Document)]
  public DateTimeOffset LastUpdated { get; set; }
  public string? LastEvent { get; set; }
  // This may or may not work.
  public List<Session>? UserSessions { get; set; }
  //TODO: Add Task[] ?
  //TODO: Add Scheduled_Sections[] ?
  // Methods
  public override string ToString() => JsonSerializer.Serialize(this);

  public void CalculateState()
  {
    // bool Conditions
    bool hasLastEvent = null != LastEvent;
    // bool hasEndOfDayEvent = LastEvent == ThisEvent.EndOfDay.ToString();
    // When there's no active sessions, UserSession == { session == null }
    // There's exactly one null session.
    // When it hasn't been initialized/registered, UserSession == null
    bool hasNoUserSessions = null == UserSessions || UserSessions.Where(s => null == s).Count() == 1;
    bool hasActiveSession = false;
    bool hasContactInInterval = LastUpdated > DateTimeOffset.Now.AddMinutes(-Collector.MaxIntervalMinutes);

    /** The conditional logical AND operator &&, also known as the
      * "short-circuiting" logical AND operator, computes the logical AND of
      * its operands. The result of x && y is true if both x and y evaluate to
      * true. Otherwise, the result is false. If x evaluates to false, y is
      * not evaluated.
      *
      * The conditional logical OR operator ||, also known as the
      * "short-circuiting" logical OR operator, computes the logical OR of its
      * operands. The result of x || y is true if either x or y evaluates to
      * true. Otherwise, the result is false. If x evaluates to true, y is not
      * evaluated.
      */
    if (!hasNoUserSessions && null != UserSessions)
      hasActiveSession = UserSessions.Any(s => s.State == "Active");

    // SomeState => Not Connected (Default state for all non-registered Devices)
    //              - May require an Unregister function somewhere
    //              - State where Lab_Guid[] only contains Default Lab GUID
    //              - Default LastEvent is null for new Clients
    if (
      !hasLastEvent ||
      (Lab_GUID.Count == 1) &&
      Lab_GUID.Contains(new Guid(Lab.Default_Lab_GUID))
    )
    {
      CurrentState = DeviceState.NotConnected;
      return;
    }
    // SomeState => Available
    //              - LastEvent == 'LogOffEvent'
    //              - LastUpdated <= 6 minutes ago
    //              - UserSessions where Session.State not contains 'Active'
    if (
      !hasActiveSession &&
      hasContactInInterval
    )
    {
      CurrentState = DeviceState.Available;
      return;
    }
    // SomeState => InUse
    //              - UserSessions where Session.State contains 'Active'
    if (
      hasActiveSession &&
      hasContactInInterval
    )
    {
      CurrentState = DeviceState.InUse;
      return;
    }

    // SomeState => Offline
    // Logically, if all we do is wait for it to stop responding, then we'll
    // always mark it offline. This will simplify events.
    // EndOfDay event will happen and then the system will be marked offline
    // after it has stopped responding past the wait time (default 6 minutes).
    //              -> LastEvent == 'EndOfDay'
    //              ( LastUpdated > 6 minutes will need a server-side check )
    //              AND LastUpdated > 6 minutes ago
    if (
      // hasEndOfDayEvent &&
      !hasContactInInterval
    )
    {
      CurrentState = DeviceState.Offline;
      return;
    }
  }

  public Client() => Lab_GUID = [];

  public Client(Registration registration)
  {
    // Type_GUID = new Guid(GUID);
    SerialNumber = registration.SerialNumber;
    HostName = registration.HostName;
    // CurrentState = DeviceState.NotConnected;
    // Using the default GUID so that it registers
    Client_GUID = Collector.DefaultGUID;
    Lab_GUID = [new Guid(Lab.Default_Lab_GUID)];
  }

  public Client(Device device)
  {
    // Type_GUID = new Guid(GUID);
    HostName = device.HostName;
    DNSHostName = device.DNSHostName;
    Client_GUID = device.Client_GUID;
    // CurrentState = DeviceState.NotConnected;
    Lab_GUID = [new Guid(Lab.Default_Lab_GUID)];
  }
}
