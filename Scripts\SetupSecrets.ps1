Import-Module 'Microsoft.PowerShell.SecretManagement', 'Microsoft.PowerShell.SecretStore'

Write-Host -ForegroundColor Green "You will need to set a temporary password."

$StoreConfig = @{
  Authentication = 'None'
  Interaction    = 'None'
  Confirm        = $false
}

Set-SecretStoreConfiguration @StoreConfig

$Vault = @{
  Name         = 'PSSecretStore'
  ModuleName   = 'Microsoft.PowerShell.SecretStore'
  DefaultVault = $true
}

Register-SecretVault @Vault

Set-Secret -Name 'LabCollectorAgent' -Secret (Get-Credential -Message 'LabCollectorAgent' -UserName 'camosun\agent_MyLabAccess')
Set-Secret -Name 'GoDaddyWildCardPFX' -Secret (Get-Credential -Message 'GoDaddyWildCardPFX' -UserName 'None')
