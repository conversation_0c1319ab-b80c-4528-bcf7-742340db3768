using collector.common.model;

using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Serializers;
using MongoDB.Driver;

namespace collector.common.service;

public class MongoDBService
{
  private static readonly string ConnectionString = "mongodb://localhost:27017/?uuidRepresentation=Standard";
  private static readonly string Database = "collectordb";
  public enum Collections
  {
    // Collection of Lab Machines running client
    ClientData,
    // Collection of AD information Device groups & members mapped to GUIDs
    LabData,
    // Collection of Sites for display (CheckRDP LabData files)
    StaticSitesData,
    // Collection of Schedule Data from ColldbRPT
    ScheduleData
  }

  // Private Properties
  private static readonly MongoClient mongoClient;
  private static readonly IMongoDatabase CollectorDB;

  private static readonly IMongoCollection<Client> Clients;
  private static readonly IMongoCollection<Lab> Labs;
  private static readonly IMongoCollection<Schedule> Schedules;
  // private static string Header = " --- [MongoDBService] ";

  static MongoDBService()
  {
    // Updated Connection String to use UUID in the Standard format (V3).
    // As long as this is maintained in the driver, these options are not needed.
    // Settings as per MongoDB recommendations:
    // TODO: This will be obsolete in V3 MongoDB Driver
    // BsonDefaults.GuidRepresentation = GuidRepresentation.Standard;
    // This doesn't work properly when collection.Find( x => x.SomeGuid == aGuid );
    BsonSerializer.RegisterSerializer(new GuidSerializer(GuidRepresentation.Standard));
    // Create an Auto-map for models
    //
    // BsonClassMap.RegisterClassMap<Lab>();
    // BsonClassMap.RegisterClassMap<Schedule>();
    // BsonClassMap.RegisterClassMap<UserSession>();

    mongoClient = new MongoClient(ConnectionString);
    CollectorDB = mongoClient.GetDatabase(Database);

    Clients = CollectorDB.GetCollection<Client>(Collections.ClientData.ToString());
    Labs = CollectorDB.GetCollection<Lab>(Collections.LabData.ToString());
    Schedules = CollectorDB.GetCollection<Schedule>(Collections.ScheduleData.ToString());

  }

  // Methods
  /*
   * Process Registration should return a Client object from the MongoDB ClientData collection
   * It will lookup via Registration.SerialNumber
   */
  /* Client CRUD */
  public static async Task<bool> ClientCreate(Client client)
  {
    await Clients.InsertOneAsync(client);
    return true;
  }

  public static async Task ClientCreate(Device device) =>
    await Clients.InsertOneAsync(new Client(device));

  public static async Task<Client> ClientRead(Client client) => await Clients.Find(
      f => f.Client_GUID == client.Client_GUID
    ).SingleOrDefaultAsync();

  public static async Task<Client> ClientRead(Guid guid) => await Clients.Find(
      f => f.Client_GUID == guid
    ).SingleOrDefaultAsync();

  // We're using AD's ObjectGUID property to create clients
  // So a client created by a device, should match that device.
  public static async Task<Client> ClientRead(Device device) => await Clients.Find(
      f => f.Client_GUID == device.Client_GUID
    ).SingleOrDefaultAsync();
  /* // Don't think we can use this.
  public static async Task<List<Client>> ClientsRead(List<Device> devices)
  {
    var clientList = new List<Client>();
    foreach (var device in devices)
    {
      var client = await Clients.Find(
        f => f.HostName == device.HostName
      ).SingleOrDefaultAsync();
      if(null != client)  clientList.Add(client);
    }
    return clientList;
  }
  */
  public static async Task<Client> ClientRead(string HostName) => await Clients.Find(
      f => f.HostName == HostName
    ).SingleOrDefaultAsync();

  // Finds a client entry that has the same hostname & serialnumber as the registration
  public static async Task<Client> ClientRead(Registration registration) => await Clients.Find(
      f =>
        f.HostName == registration.HostName &&
        f.SerialNumber == registration.SerialNumber
    ).SingleOrDefaultAsync();

  public static async Task<Client> ClientRead(UserSession userSession) => await Clients.Find(
      f => f.Client_GUID == userSession.Client_GUID
    ).SingleOrDefaultAsync();

  public static async Task<List<Client>> ClientRead(Lab lab)
  {
    var result = await Clients.Find(
      f => f.Lab_GUID.Contains(lab.Lab_GUID)
    ).ToListAsync();
    return [.. result.OrderBy(c => c.HostName)];
  }

  public static async Task<List<Client>> ClientReadOffline() => await Clients.Find(
      f =>
        // We don't care about not connected devices
        f.CurrentState != DeviceState.NotConnected &&
        // And we don't care about devices already offline
        f.CurrentState != DeviceState.Offline &&
        // We care about devices that haven't updated in reasonable interval.
        f.LastUpdated <= DateTimeOffset.Now.AddMinutes(-Collector.MaxIntervalMinutes)
    ).ToListAsync();
  public static async Task<bool> ClientUpdate(Client client)
  {
    client.CalculateState();
    var result = await Clients.ReplaceOneAsync(
      f => f.Client_GUID == client.Client_GUID, client
    );
    return result.IsAcknowledged;
  }
  public static async Task<bool> ClientDelete(Client client)
  {
    var result = await Clients.DeleteOneAsync(
      f => f.Client_GUID == client.Client_GUID
    );
    return result.IsAcknowledged;
  }

  /* Schedule CRUD */
  public static async Task<bool> ScheduleCreate(Schedule schedule)
  {
    await Schedules.InsertOneAsync(schedule);
    return true;
  }

  public static async Task<Schedule> ScheduleRead(Schedule schedule) => await Schedules.Find(
      f => f.Schedule_GUID == schedule.Schedule_GUID
    ).SingleOrDefaultAsync();
  public static async Task<Schedule> ScheduleRead(Guid guid) => await Schedules.Find(
      f => f.Schedule_GUID == guid
    ).SingleOrDefaultAsync();

  public static async Task<bool> ScheduleUpdate(Schedule schedule)
  {
    var result = await Schedules.ReplaceOneAsync(
      f => f.Schedule_GUID == schedule.Schedule_GUID, schedule
    );
    return result.IsAcknowledged;
  }
  public static async Task<bool> ScheduleDelete(Schedule schedule)
  {
    var result = await Schedules.DeleteOneAsync(
      f => f.Schedule_GUID == schedule.Schedule_GUID
    );
    return result.IsAcknowledged;
  }

  /* Lab CRUD */
  public static async Task<bool> LabCreate(Lab lab)
  {
    await Labs.InsertOneAsync(lab);
    return true;
  }
  public static async Task<Lab> LabRead(Lab lab) => await Labs.Find(
      f => f.Lab_GUID == lab.Lab_GUID
    ).SingleOrDefaultAsync();

  public static async Task<List<string>> LabReadAllRoomNames() => await Labs.Find(
      f => true
    ).Project(
      f => f.RoomName
    ).ToListAsync();

  public static async Task<Lab> LabRead(Guid guid) => await Labs.Find(
      f => f.Lab_GUID == guid
    ).SingleOrDefaultAsync();

  public static async Task<Lab> LabRead(string RoomName) => await Labs.Find(
      f => f.RoomName == RoomName
    ).SingleOrDefaultAsync();

  // I don't know if we can use this, as it's One Client -> Many Labs
  // This returns one to one, even if there are multiple )-:
  /*
  public static async Task<Lab> LabRead(Client client)
  {
    // This will need to be tested.
    var query = Builders<Lab>.Filter.ElemMatch(
      l => l.Devices,
      d => d.HostName == client.HostName
    );
    return await Labs.Find(
      //query
      lab => lab.Devices.Where(
        d => d.HostName == client.HostName
      ).Count() != 0
    ).SingleOrDefaultAsync();
  }*/

  // Instead, we just need to return one device reference from any lab which
  // we can use to lookup the client entry.
  public static async Task<Device?> LabRead(Registration registration)
  {
    // This is the MongoSH query that does what I want
    // {Devices: { $elemMatch: {HostName: "<HostName>" }}}
    // Example from StackExchange
    // var findFluent = collection.Find(Builders<Foo>.Filter.ElemMatch(
    // foo => foo.Bars,
    // foobar => foobar.BarId == "123"));

    // Get one lab
    // Build a filter based on $elemmatch
    var labFilter = Builders<Lab>.Filter.ElemMatch(
      lab => lab.Devices,
      device => device.HostName == registration.HostName
    );
    // Find a lab that matches the filter
    var lab = await Labs.Find(labFilter).FirstOrDefaultAsync();

    // Get one lab
    // This doesn't work, Unsupported filter error/exception
    // var lab = await Labs.Find(
    //   // For a given lab
    //   lab => lab.Devices.Exists(
    //     // That has a device with a hostname that matches the registration
    //     device => device.HostName == registration.HostName
    //   )
    //   // return the first lab or null
    // ).FirstOrDefaultAsync();

    // If the lab is null, return early as null
    return lab?.Devices.Where(
      // ...that matches the hostname in the registration
      device => device.HostName == registration.HostName
    // ... or return null.
    ).FirstOrDefault();
  }

  public static async Task<List<Lab>> LabRead() => await Labs.Find(f => true).ToListAsync();

  public static async Task<bool> LabUpdate(Lab lab)
  {
    // Set the update stamp to now.
    lab.LastUpdated = DateTimeOffset.Now;
    var result = await Labs.ReplaceOneAsync(
      f => f.Lab_GUID == lab.Lab_GUID, lab
    );
    return result.IsAcknowledged;
  }
  public static async Task<bool> LabDelete(Lab lab)
  {
    var result = await Labs.DeleteOneAsync(
      f => f.Lab_GUID == lab.Lab_GUID
    );
    return result.IsAcknowledged;
  }

  /* Watch Methods */
  //*
  public static async Task LabWatch()
  {
    using var cursor = await Labs.WatchAsync();
    await cursor.ForEachAsync(
      async change =>
      {
        if (change.OperationType == ChangeStreamOperationType.Replace)
        {
          await Console.Out.WriteLineAsync($"--- LabWatch: {change.FullDocument}");
        }
      });
  }//*/

  public static async Task ClientWatch()
  {
    using var cursor = await Clients.WatchAsync();
    await cursor.ForEachAsync(
      async change =>
      {
        if (change.OperationType == ChangeStreamOperationType.Replace)
        {
          await Console.Out.WriteLineAsync($"--- ClientWatch: {change.FullDocument}");
        }
      });
  }
}
