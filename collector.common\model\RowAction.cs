namespace collector.common.model;

public class RowAction
{
  public string? Class { get; set; }
  public string? Use { get; set; }
  public string? RDP_URI { get; set; }
  public string? Button { get; set; }

  public static RowAction GetAction(Client client)
  {
    return client.CurrentState switch
    {
      DeviceState.NotConnected => new RowAction
      {
        Class = "busy",
        Use = "Not Connected",
        RDP_URI = "#",
        Button = "btn disabled"
      },
      DeviceState.Available => new RowAction
      {
        Class = "free",
        Use = "Available",
        RDP_URI = "#RDP_URI",
        Button = "btn btn-success"
      },
      DeviceState.InUse => new RowAction
      {
        Class = "busy",
        Use = "In Use",
        RDP_URI = "#",
        Button = "btn disabled"
      },
      DeviceState.Offline => new RowAction
      {
        Class = "busy",
        Use = "Offline",
        RDP_URI = "#",
        Button = "btn disabled"
      },
      _ => new RowAction
      {
        Class = "busy",
        Use = "Unavailable",
        RDP_URI = "#",
        Button = "btn disabled"
      }
    };
  }
}
