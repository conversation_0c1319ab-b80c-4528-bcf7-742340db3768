using collector.common.model;
using collector.common.service;
using collector.webapp.Handlers;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.StaticFiles;

namespace collector.webapp.Pages;

[Authorize]
public class LabDataModel(
  ILogger<LabDataModel> logger,
  IConfiguration configuration
) : PageModel
{
  private readonly ILogger<LabDataModel> _logger = logger;
  private readonly List<string> _validPages =
  [
    "/Student",
    "/Staff",
    "/Admin"
  ];
  private readonly IConfiguration _configuration = configuration;
  public LabCalendarOptions? LabCalendarOptions { get; private set; }

  [BindProperty(SupportsGet = true)]
  public string RoomName { get; set; }
  public Lab Lab;

  public List<Client> Clients;

  public async Task<IActionResult> OnGetAsync()
  {
    // Get the calendar config
    LabCalendarOptions = _configuration.GetSection(LabCalendarOptions.LabCalendar).Get<LabCalendarOptions>();
    // Check the route Data
    var routeDataJson = System.Text.Json.JsonSerializer.Serialize(RouteData);
    _logger.LogInformation("RouteData : [{routeDataJson}]", routeDataJson);
    // Get the page value
    var page = RouteData.Values["page"] as string;
    // Check page to see if it's valid
    if (null != page && !_validPages.Contains(page)) return NotFound();

    // Check the RoomName to see if it's valid.
    bool isValidRoomName = await CheckRoomName();
    _logger.LogInformation("RoomName:[{RoomName}:{isValidRoomName}]", RoomName, isValidRoomName);
    if (!isValidRoomName) return NotFound();

    // Get the Lab from the DB
    Lab = await MongoDBService.LabRead(RoomName);
    if (null == Lab) return NotFound();

    // Check the Lab is enabled and the permissions against the page
    bool isPermitted = Lab.Enabled == true && page switch
    {
      "/Student" => Lab.IsStaffOnly == false && Lab.IsAdminOnly == false,
      "/Staff" => Lab.IsAdminOnly == false,
      "/Admin" => true,
      _ => false
    };

    _logger.LogInformation("Found Lab [{RoomName}:{Lab_GUID}] Permission [{page}:{isPermitted}]", Lab.RoomName, Lab.Lab_GUID, page, isPermitted);
    if (!isPermitted) return NotFound();

    // This doesn't work when there are no Clients.
    Clients = await MongoDBService.ClientRead(Lab);
    _logger.LogInformation("Found [{Count}] Clients", Clients.Count);
    if (Clients.Count != 0)
      Lab.LastUpdated = Clients.Max(c => c.LastUpdated);

    return Page();
  }

  public async Task<bool> CheckRoomName()
  {
    // Check the RoomName is not null
    if (null == RoomName) return false;

    // Get the list of Names
    var validRoomNames = await MongoDBService.LabReadAllRoomNames();

    // Check the RoomName against the list.
    bool isValidName = validRoomNames.Contains(RoomName);
    return isValidName;
  }

  public async Task<FileStreamResult> OnPostRDPAsync(string theDNSHostName)
  {
    //https://www.learnrazorpages.com/razor-pages/handler-methods#parameters-in-handler-methods
    //https://darchuk.net/2019/05/31/asp-net-core-web-api-returning-a-filestream/
    _logger.LogInformation(" --- [OnPostRDPAsync] Generating RDP for {theDNSHostName}", theDNSHostName);
    // Generate the rdpString for the client
    var rdpStrings = new List<string>(){
          $"full address:s:{theDNSHostName}",
          "audiocapturemode:i:1",
          "audiomode:i:0",
          "dynamicresolution:i:1"
        };
    // Create a byte[] response from the strings
    var rdpResponse = System.Text.Encoding.UTF8.GetBytes(
        string.Join(Environment.NewLine, rdpStrings)
      );
    // Define an output file name.
    var fileName = $"{theDNSHostName}.rdp";

    //Attempt to generate a contentType
    var provider = new FileExtensionContentTypeProvider();
    if (!provider.TryGetContentType(fileName, out string? contentType))
      //default type
      contentType = "application/rdp";

    // Create a MemoryStream Object for the StreamWriter
    var ms = new MemoryStream();

    // Write the rdpResponse to the stream
    await ms.WriteAsync(rdpResponse);

    // Rewind the MemoryStream for handoff
    ms.Seek(0, SeekOrigin.Begin);

    _logger.LogInformation(" --- [OnPostRDPAsync] Generated [{fileName}:{contentType}]", fileName, contentType);

    // Return the File response
    return File(ms, contentType, fileName);
    // ASP.NET Core disposes of the ms object when it's finished.
  }
}
