using System;
using System.Collections.Generic;
using System.Text.Json;

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace collector.common.model;

public class Schedule : LabData
{
  public const string GUID = "9ab2482c-0548-49da-b39d-392e1a2fff29";
  public const string DefaultScheduleGUID = "7338b54f-e84b-4d1c-8464-a902f5b8453d";
  public override Guid Type_GUID { get; set; } = new Guid(GUID);

  [BsonId]
  public Guid Schedule_GUID { get; set; }

  [BsonRepresentation(BsonType.Document)]
  public DateTimeOffset CurrentDate { get; set; }

  public List<Section> SectionSchedules { get; set; } = [];
  public override string ToString() => JsonSerializer.Serialize(this);

}
