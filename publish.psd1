@{
  Publish  = 'LabCollector'
  Projects = @{
    CollectorClient = @{
      Files  = @(
        'Scripts/CollectorWrapper.ps1'
        'Scripts/CollectorWrapper.cfg.psd1'
        'collector.client/publish/collector.client.exe'
      )
      Target = 'Client'
    }
    CollectorServer = @{
      Files  = @(
        'Scripts/Check_RDPLogin.ps1'
        'Scripts/ProxyWrapper.ps1'
        'Scripts/CollectorWrapper.cfg.psd1'
        'Scripts/collector.common.ps1'
        'Scripts/Interurban.psd1'
        'Scripts/Lansdowne.psd1'
        'Scripts/LabData.csv'
        'Scripts/None.psd1'
        'Scripts/index_footer.txt'
        'Scripts/index_header.txt'
        'collector.client/publish/collector.client.exe'
        'collector.server/publish/collector.server.exe'
        'collector.server/publish/appsettings.json'
      )
      Target = 'Server'
    }
    GPODeployScript = @{
      Files  = @(
        'Scripts/DeployLabCollector.ps1'
        'Scripts/DeployServer.ps1'
        'Scripts/DeployServer.cfg.psd1'
        'Scripts/SetupModules.ps1'
        'Scripts/SetupSecrets.ps1'
      )
      Target = 'Setup'
    }
    WebAppPublish   = @{
      Directory = 'collector.webapp/bin/Release/net8.0/win-x64/publish'
      Target    = 'WebApp/publish'
    }
  }
}
