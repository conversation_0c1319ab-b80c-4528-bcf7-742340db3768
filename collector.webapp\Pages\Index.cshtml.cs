﻿using collector.common.model;
using collector.common.service;

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace collector.webapp.Pages;

public class IndexModel(ILogger<IndexModel> logger) : PageModel
{
  private readonly ILogger<IndexModel> _logger = logger;

  public List<Lab> Labs { get; set; }

  public List<Lab> StudentLabs { get; set; }
  public List<Lab> StaffLabs { get; set; }

  public List<string> Campuses { get; set; }
  public List<string> Buildings { get; set; }

  public async Task<IActionResult> OnGetAsync()
  {
    // Get a list of all Enabled labs
    Labs = (
      await MongoDBService.LabRead()
      ).Where(
        l => l.Enabled == true
      ).ToList();

    // Build a list of Staff Lab Names
    StaffLabs = Labs.Where(
        lab => lab.IsStaffOnly == true
      ).ToList();

    // Build a list of Student Lab Names
    StudentLabs = Labs.Where(
        lab => lab.IsAdminOnly == false && lab.IsStaffOnly == false
      ).ToList();

    Campuses = StudentLabs.Select(
      lab => lab.Campus
    ).Distinct().ToList();

    return Page();
  }
}
