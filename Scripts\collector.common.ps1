#region Session
class Session {
  [string]$UserName
  [string]$SessionName
  [string]$ID
  [string]$State
  [string]$IdleTime
  [DateTimeOffset]$LogonTime

  static [hashtable]$CSVOptions = @{
    Delimiter = ';'
    Header    = @(
      'UserName'
      'SessionName'
      'ID'
      'State'
      'IdleTime'
      'LogonTime'
    )
  }

  static [timespan]$MaxIdleTime = [timespan]'01:15:00'
  static [string]$NameTemplate = '{0}_{1}.session'
}
#endregion Session

#region LabData
class LabData {
  [guid]$Type_GUID
}
#endregion LabData

#region Client Class
enum ThisEvent {
  LogOffEvent
  Boot
  Logon
  RemoteConnect
  RemoteDisconnect
  SessionLock
  SessionUnlock
  Heartbeat
  EndOfDay
}

enum DeviceState {
  NotConnected
  Available
  InUse
  Offline
}

class Client : LabData {
  static [string]$GUID = '17ff2b01-de2c-422f-8e50-35bd09840480'
  [GUID]$Type_GUID = [guid]::new([Client]::GUID)
  [ValidateNotNullOrEmpty()]
  [string]$HostName
  [string]$SerialNumber
  [string]$DNSHostName
  [DeviceState]$CurrentState = [DeviceState]::NotConnected
  [Guid]$Client_GUID
  [Guid]$Server_GUID
  [Guid[]]$Lab_GUID
  [DateTimeOffset]$LastUpdated
  [string]$LastEvent
  [Session[]]$UserSessions
}
#endregion Client Class

#region Registration
class Registration : LabData {
  static [string]$GUID = '2838835a-6b58-4d7e-b83d-7401aca79aee'
  [GUID]$Type_GUID = [guid]::new([Registration]::GUID)
  [ValidateNotNullOrEmpty()]
  [string]$HostName
  [ValidateNotNullOrEmpty()]
  [string]$SerialNumber
  [bool]$IsRegistering
}
#endregion Registration

#region Device
class Device {
  [ValidateNotNullOrEmpty()]
  [string]$HostName
  [ValidateNotNullOrEmpty()]
  [string]$DNSHostName
  [Guid]$Client_GUID
}
#endregion Device

#region Lab
class Lab : LabData {
  static [string]$GUID = '620e8f12-6730-42eb-91e9-e6ed9ebfe6ce'
  static [guid]$Default_Lab_GUID = [guid]::new('17a8b301-d86f-43eb-a5e8-9181400ac421')
  [GUID]$Type_GUID = [guid]::new([Lab]::GUID)
  [Guid]$Lab_GUID
  [ValidateNotNullOrEmpty()]
  [string]$RoomName
  [ValidateNotNullOrEmpty()]
  [string]$Campus
  [string]$Building
  [string]$Description
  [bool]$IsAdminOnly
  [bool]$IsStaffOnly
  [bool]$HasSchedule
  [bool]$Enabled
  [DateTimeOffset]$LastUpdated
  [string]$DeviceGroup
  [Device[]]$Devices
  [string[]]$DefaultRDPGroups
}
#endregion Lab

#region UserSession
class UserSession : LabData {
  static [string]$GUID = '59a24efa-a26c-485c-be83-3f6b66a28b5e'
  [GUID]$Type_GUID = [guid]::new([UserSession]::GUID)
  [Guid]$Client_GUID
  [DateTimeOffset]$SampleTime
  [string]$ThisEvent
  [Session[]]$Client_Sessions
}
#endregion UserSession
