﻿@page
@using collector.common.model
@model IndexModel
@{
  ViewData["Title"] = "My Lab Access";
}

<!-- Start of Body -->
<section id="content">
  <!-- section content -->
  <div class="row">
    <!-- div row -->
    <div class="span4">
      <!-- div span4 -->
      <h1>Welcome to @ViewData["Title"]</h1>
      <table class="table">
        <thead>
          <tr>
            <th scope="col">Student Labs</th>
          </tr>
        </thead>
        @foreach (var campus in @Model.Campuses)
        {
          List<Lab> campuslabs = Model.StudentLabs.Where(
            lab => lab.Campus == campus
          ).ToList();

          @foreach (var building in @campuslabs.Select(lab => lab.Building).Distinct())
          {
            <thead>
              <tr>
                <th scope="col">@building at @campus</th>
              </tr>
            </thead>
            <tbody>
              @foreach (var lab in @Model.StudentLabs.Where(lab => lab.Campus == campus && lab.Building == building))
              {
                <tr class="free">
                  <td><a asp-page="/Student" asp-route-RoomName="@lab.RoomName">@lab.RoomName</a>
                  </td>
                </tr>
              }
            </tbody>
          }
        }
        <thead>
          <tr>
            <th scope="col"> Instructor Stations </th>
          </tr>
        </thead>
        <tbody>
          @foreach (var lab in @Model.StaffLabs)
          {
            <tr>
              <td><a asp-page="/Staff" asp-route-RoomName="@lab.RoomName">@lab.RoomName</a></td>
            </tr>
          }
        </tbody>
      </table>
    </div> <!-- div span 4 -->
    <div class="span8"></div>
  </div> <!-- div row -->
</section> <!-- section content -->
<!-- End of Body -->