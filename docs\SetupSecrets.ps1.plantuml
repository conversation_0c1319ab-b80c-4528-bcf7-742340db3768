@startuml SetupSecrets
start
partition #Salmon SetupSecrets.ps1 {
  :Run: SetupSecrets.ps1 (as "agent_MyLabAccess")]
  :Import-<PERSON><PERSON><PERSON>
  - 'Microsoft.PowerShell.SecretManagement',
  - 'Microsoft.PowerShell.SecretStore'>

  :Set-SecretStoreConfig
  - This involves setting a temporary password>

  :Register-SecretVault>
  :Set-Secret
  -Name 'LabCollectorAgent'
  -Secret (Get-Credential -Message "LabCollectorAgent")>
  :Set-Secret
  -Name 'GoDaddyWildCardPFX'
  -Secret (Get-Credential -Message "GoDaddyWildCardPFX")>
}
stop
@enduml